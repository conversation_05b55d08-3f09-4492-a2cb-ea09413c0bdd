action:
  click_delay: 0.1
  fail_safe: true
  max_wait_time: 10
  pause: 0.1
  screenshot_delay: 0.2
  type_delay: 0.05
gui:
  auto_save_config: true
  log_lines_limit: 1000
  show_preview: true
  theme: default
  window_size: 1200x800
  window_title: 屏幕识别与自动化工具
logging:
  backup_count: 5
  console_handler: true
  file_handler: true
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  log_file: logs/automation.log
  max_bytes: 10485760
ocr:
  confidence_threshold: 0.8
  det_db_box_thresh: 0.6
  det_db_thresh: 0.3
  det_db_unclip_ratio: 1.5
  enable_mkldnn: true
  lang: ch
  use_angle_cls: true
  use_gpu: false
paths:
  logs_dir: logs
  screenshots_dir: temp/screenshots
  temp_dir: temp
  templates_dir: templates
preprocessing:
  binary_threshold: 127
  enable_binary: true
  enable_sobel: true
  enable_tophat: true
  kernel_size:
  - 17
  - 17
  sobel_threshold: 75
screenshot:
  confidence: 0.9
  grayscale: false
  region: null
template_match:
  enable_multi_scale: true
  max_matches: 10
  method: cv2.TM_CCOEFF_NORMED
  multi_scale_factors:
  - 0.8
  - 1.0
  - 1.2
  - 1.5
  threshold: 0.85
