2025-07-26 07:50:26,901 - main - INFO - ==================================================
2025-07-26 07:50:26,905 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 07:50:26,906 - main - INFO - ==================================================
2025-07-26 07:50:28,178 - main - INFO - 所有依赖包检查通过
2025-07-26 07:50:28,183 - main - INFO - 目录结构检查完成
2025-07-26 07:50:28,183 - main - INFO - 启动GUI界面...
2025-07-26 07:50:28,622 - screen - INFO - ScreenCapture初始化完成
2025-07-26 07:50:29,719 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 07:50:29,720 - ocr - INFO - OCREngine初始化完成
2025-07-26 07:50:29,721 - match - INFO - TemplateMatch初始化完成
2025-07-26 07:50:29,721 - screen - INFO - ScreenCapture初始化完成
2025-07-26 07:50:29,722 - match - INFO - TemplateMatch初始化完成
2025-07-26 07:50:29,723 - action - INFO - ActionExecutor初始化完成
2025-07-26 07:50:29,767 - gui - INFO - 主窗口初始化完成
2025-07-26 07:51:17,011 - ocr - INFO - OCR识别完成，找到39个文本区域
2025-07-26 07:51:31,611 - action - INFO - 临时文件清理完成
2025-07-26 07:51:31,655 - main - INFO - 应用程序正常退出
2025-07-26 08:29:22,144 - main - INFO - ==================================================
2025-07-26 08:29:22,145 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 08:29:22,145 - main - INFO - ==================================================
2025-07-26 08:29:23,606 - main - INFO - 所有依赖包检查通过
2025-07-26 08:29:23,607 - main - INFO - 目录结构检查完成
2025-07-26 08:29:23,608 - main - INFO - 启动GUI界面...
2025-07-26 08:29:24,093 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:29:25,488 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 08:29:25,489 - ocr - INFO - OCREngine初始化完成
2025-07-26 08:29:25,490 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:29:25,491 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:29:25,492 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:29:25,493 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:29:25,579 - gui - INFO - 主窗口初始化完成
2025-07-26 08:30:05,970 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,972 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,972 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,976 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,978 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,980 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,980 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,980 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,981 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:05,983 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,983 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,983 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:06,089 - action - INFO - 点击坐标: (102, 201), 按钮: left
2025-07-26 08:30:06,090 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:06,091 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:06,091 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:06,093 - action - INFO - 按键: enter, 次数: 1
2025-07-26 08:30:06,094 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:06,095 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:06,096 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:06,097 - action - INFO - 输入文本: '测试文本'
2025-07-26 09:36:05,229 - main - INFO - ==================================================
2025-07-26 09:36:05,229 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 09:36:05,230 - main - INFO - ==================================================
2025-07-26 09:36:10,428 - main - INFO - 所有依赖包检查通过
2025-07-26 09:36:10,429 - main - INFO - 目录结构检查完成
2025-07-26 09:36:10,429 - main - INFO - 启动GUI界面...
2025-07-26 09:36:10,893 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:36:11,969 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:36:11,970 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:36:11,971 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:36:11,971 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:36:11,971 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:36:11,972 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:36:12,074 - gui - INFO - 主窗口初始化完成
2025-07-26 09:37:28,246 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:37:28,248 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:37:28,250 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:37:28,251 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:37:28,252 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:37:28,254 - test_ocr_search - INFO - 开始测试OCR搜索功能
2025-07-26 09:37:28,298 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:37:28,299 - test_ocr_search - INFO - 测试1: 基本OCR识别
2025-07-26 09:37:57,590 - ocr - INFO - OCR识别完成，找到78个文本区域
2025-07-26 09:37:57,621 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:37:57,623 - ocr - INFO - 高级搜索完成，找到2个匹配结果
2025-07-26 09:37:57,625 - gui - ERROR - 显示搜索结果失败: 'MainWindow' object has no attribute 'status_var'
2025-07-26 09:37:59,225 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:37:59,245 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:38:02,259 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:38:02,260 - test_ocr_search - INFO - 识别到2个文本区域
2025-07-26 09:38:02,260 - test_ocr_search - INFO -   文本: 'Screen Recoqnition', 置信度: 0.979, 中心: (420, 142)
2025-07-26 09:38:02,261 - test_ocr_search - INFO -   文本: 'Test', 置信度: 0.803, 中心: (101, 186)
2025-07-26 09:38:02,261 - test_ocr_search - INFO - 测试2: 精确搜索
2025-07-26 09:38:11,248 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:11,249 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:11,249 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:11,250 - test_ocr_search - INFO - 精确搜索找到1个结果
2025-07-26 09:38:11,250 - test_ocr_search - INFO - 测试3: 包含搜索
2025-07-26 09:38:18,474 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:18,475 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:18,476 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:18,476 - test_ocr_search - INFO - 包含搜索找到1个结果
2025-07-26 09:38:18,477 - test_ocr_search - INFO - 测试4: 模糊搜索
2025-07-26 09:38:22,951 - action - INFO - 点击坐标: (1271, 726), 按钮: left
2025-07-26 09:38:22,960 - action - INFO - 成功点击OCR结果: '坐标' at (1269, 726)
2025-07-26 09:38:22,971 - action - INFO - 成功点击搜索结果: '坐标' -> '坐标'
2025-07-26 09:38:22,976 - action - INFO - 批量点击 1: 坐标
2025-07-26 09:38:24,203 - action - INFO - 点击坐标: (231, 264), 按钮: left
2025-07-26 09:38:24,214 - action - INFO - 成功点击OCR结果: '搜索文本：坐标' at (233, 264)
2025-07-26 09:38:24,216 - action - INFO - 成功点击搜索结果: '坐标' -> '搜索文本：坐标'
2025-07-26 09:38:24,217 - action - INFO - 批量点击 2: 搜索文本：坐标
2025-07-26 09:38:24,217 - action - INFO - 批量点击完成，成功点击2个结果
2025-07-26 09:38:25,672 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:25,673 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:25,674 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:25,674 - test_ocr_search - INFO - 模糊搜索找到1个结果
2025-07-26 09:38:25,675 - test_ocr_search - INFO - 测试5: 批量搜索
2025-07-26 09:38:33,229 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:33,230 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:33,231 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:33,232 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:33,233 - ocr - INFO - 批量搜索完成，总共找到3个匹配结果
2025-07-26 09:38:33,233 - test_ocr_search - INFO - 批量搜索完成，找到3组结果
2025-07-26 09:38:33,234 - test_ocr_search - INFO - 测试6: 图像标注
2025-07-26 09:38:33,480 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:38:33,482 - test_ocr_search - INFO - 标注图像已保存到 temp/test_annotated.png
2025-07-26 09:38:33,483 - test_ocr_search - INFO - 测试7: 搜索统计
2025-07-26 09:38:33,483 - test_ocr_search - INFO - 搜索统计: {'total_results': 1, 'average_confidence': 0.9983128309249878, 'average_match_score': 0.8888888888888888, 'mode_distribution': {'fuzzy': 1}, 'confidence_distribution': {'high': 1, 'medium': 0, 'low': 0}, 'best_match': {'ocr_result': {'text': 'Hello', 'confidence': 0.9983128309249878, 'bbox': [[53.0, 54.0], [249.0, 58.0], [248.0, 105.0], [52.0, 102.0]], 'center': (150, 79), 'rect': (52.0, 54.0, 197.0, 51.0), 'area': 10047.0, 'region_id': 'region_2480628010288'}, 'match_score': 0.8888888888888888, 'match_text': 'Hello', 'search_term': 'Helo', 'search_mode': 'fuzzy'}}
2025-07-26 09:38:33,484 - test_ocr_search - INFO - 测试8: 动作队列
2025-07-26 09:38:33,484 - test_ocr_search - INFO - 动作队列创建完成，包含2个动作
2025-07-26 09:38:33,485 - test_ocr_search - INFO - 所有测试完成！
2025-07-26 09:38:34,465 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:38:34,466 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:38:34,470 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:38:34,470 - test_ocr_search - INFO - 测试搜索模式: exact, 搜索词: 'Hello World'
2025-07-26 09:38:41,551 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:38:41,552 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:38:41,553 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:38:41,554 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:38:41,554 - test_ocr_search - INFO - 测试搜索模式: contains, 搜索词: 'World'
2025-07-26 09:38:48,725 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:48,726 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:48,727 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:48,728 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:38:48,728 - test_ocr_search - INFO -     匹配: 'World', 分数: 1.000
2025-07-26 09:38:48,729 - test_ocr_search - INFO - 测试搜索模式: fuzzy, 搜索词: 'Helo World'
2025-07-26 09:38:58,815 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:58,816 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:38:58,816 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:38:58,817 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:38:58,817 - test_ocr_search - INFO - 测试搜索模式: similarity, 搜索词: 'Hello'
2025-07-26 09:39:06,660 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:06,661 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:06,661 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:06,662 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:39:06,663 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:39:06,663 - test_ocr_search - INFO - 测试搜索模式: regex, 搜索词: 'Hello.*'
2025-07-26 09:39:14,810 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:14,811 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:14,812 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:14,812 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:39:14,813 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:39:18,100 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:39:18,100 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:39:18,101 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:39:18,101 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:39:18,102 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:39:18,102 - test_ocr_search - INFO - 开始测试OCR搜索功能
2025-07-26 09:39:18,105 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:39:18,105 - test_ocr_search - INFO - 测试1: 基本OCR识别
2025-07-26 09:39:25,360 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:39:25,360 - test_ocr_search - INFO - 识别到2个文本区域
2025-07-26 09:39:25,361 - test_ocr_search - INFO -   文本: 'Screen Recoqnition', 置信度: 0.979, 中心: (420, 142)
2025-07-26 09:39:25,361 - test_ocr_search - INFO -   文本: 'Test', 置信度: 0.803, 中心: (101, 186)
2025-07-26 09:39:25,362 - test_ocr_search - INFO - 测试2: 精确搜索
2025-07-26 09:39:32,340 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:32,341 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:32,342 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:32,342 - test_ocr_search - INFO - 精确搜索找到1个结果
2025-07-26 09:39:32,343 - test_ocr_search - INFO - 测试3: 包含搜索
2025-07-26 09:39:39,698 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:39,699 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:39,700 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:39,701 - test_ocr_search - INFO - 包含搜索找到1个结果
2025-07-26 09:39:39,701 - test_ocr_search - INFO - 测试4: 模糊搜索
2025-07-26 09:39:47,392 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:47,393 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:47,393 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:47,395 - test_ocr_search - INFO - 模糊搜索找到1个结果
2025-07-26 09:39:47,396 - test_ocr_search - INFO - 测试5: 批量搜索
2025-07-26 09:39:55,389 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:55,390 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:55,391 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:55,392 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:55,393 - ocr - INFO - 批量搜索完成，总共找到3个匹配结果
2025-07-26 09:39:55,394 - test_ocr_search - INFO - 批量搜索完成，找到3组结果
2025-07-26 09:39:55,394 - test_ocr_search - INFO - 测试6: 图像标注
2025-07-26 09:39:55,721 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:39:55,725 - test_ocr_search - INFO - 标注图像已保存到 temp/test_annotated.png
2025-07-26 09:39:55,726 - test_ocr_search - INFO - 测试7: 搜索统计
2025-07-26 09:39:55,728 - test_ocr_search - INFO - 搜索统计: {'total_results': 1, 'average_confidence': 0.9983128309249878, 'average_match_score': 0.8888888888888888, 'mode_distribution': {'fuzzy': 1}, 'confidence_distribution': {'high': 1, 'medium': 0, 'low': 0}, 'best_match': {'ocr_result': {'text': 'Hello', 'confidence': 0.9983128309249878, 'bbox': [[53.0, 54.0], [249.0, 58.0], [248.0, 105.0], [52.0, 102.0]], 'center': (150, 79), 'rect': (52.0, 54.0, 197.0, 51.0), 'area': 10047.0, 'region_id': 'region_2952611990832'}, 'match_score': 0.8888888888888888, 'match_text': 'Hello', 'search_term': 'Helo', 'search_mode': 'fuzzy'}}
2025-07-26 09:39:55,729 - test_ocr_search - INFO - 测试8: 动作队列
2025-07-26 09:39:55,730 - test_ocr_search - INFO - 动作队列创建完成，包含2个动作
2025-07-26 09:39:55,731 - test_ocr_search - INFO - 所有测试完成！
2025-07-26 09:39:56,812 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:39:56,813 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:39:56,816 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:39:56,816 - test_ocr_search - INFO - 测试搜索模式: exact, 搜索词: 'Hello World'
2025-07-26 09:40:04,653 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:40:04,654 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:40:04,655 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:40:04,656 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:40:04,656 - test_ocr_search - INFO - 测试搜索模式: contains, 搜索词: 'World'
2025-07-26 09:40:11,812 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:11,813 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:40:11,814 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:40:11,815 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:40:11,815 - test_ocr_search - INFO -     匹配: 'World', 分数: 1.000
2025-07-26 09:40:11,816 - test_ocr_search - INFO - 测试搜索模式: fuzzy, 搜索词: 'Helo World'
2025-07-26 09:40:20,164 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:20,166 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:40:20,167 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:40:20,167 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:40:20,169 - test_ocr_search - INFO - 测试搜索模式: similarity, 搜索词: 'Hello'
2025-07-26 09:40:27,459 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:27,460 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:40:27,461 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:40:27,462 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:40:27,462 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:40:27,463 - test_ocr_search - INFO - 测试搜索模式: regex, 搜索词: 'Hello.*'
2025-07-26 09:40:34,536 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:34,537 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:40:34,538 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:40:34,538 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:40:34,539 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:44:08,643 - main - INFO - ==================================================
2025-07-26 09:44:08,643 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 09:44:08,643 - main - INFO - ==================================================
2025-07-26 09:44:09,968 - main - INFO - 所有依赖包检查通过
2025-07-26 09:44:09,969 - main - INFO - 目录结构检查完成
2025-07-26 09:44:09,969 - main - INFO - 启动GUI界面...
2025-07-26 09:44:10,016 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:44:10,970 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:44:10,970 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:44:10,971 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:44:10,971 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:44:10,972 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:44:10,973 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:44:11,006 - gui - INFO - 主窗口初始化完成
2025-07-26 09:45:38,368 - test_complete - INFO - 开始完整功能测试...
2025-07-26 09:45:38,369 - test_complete - INFO - === 测试所有搜索模式 ===
2025-07-26 09:45:40,502 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:45:40,503 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:45:40,510 - test_complete - INFO - 综合测试图像已保存
2025-07-26 09:45:40,510 - test_complete - INFO - 测试: 精确匹配测试
2025-07-26 09:45:40,511 - test_complete - INFO -   搜索词: 'Login Button', 模式: exact
2025-07-26 09:45:53,620 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:45:53,621 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:45:53,621 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:45:53,622 - test_complete - INFO -   找到 0 个结果 (预期: 1)
2025-07-26 09:45:53,622 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:45:53,623 - test_complete - INFO - 测试: 包含匹配测试（忽略大小写）
2025-07-26 09:45:53,623 - test_complete - INFO -   搜索词: 'login', 模式: contains
2025-07-26 09:46:06,918 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:06,918 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:46:06,919 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:46:06,919 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:46:06,920 - test_complete - INFO -     结果 1: 'Login Button' (分数: 0.417)
2025-07-26 09:46:06,920 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:46:06,921 - test_complete - INFO - 测试: 模糊匹配测试（拼写错误）
2025-07-26 09:46:06,921 - test_complete - INFO -   搜索词: 'Logn', 模式: fuzzy
2025-07-26 09:46:20,495 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:20,496 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:46:20,497 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:46:20,497 - test_complete - INFO -   找到 0 个结果 (预期: 1)
2025-07-26 09:46:20,498 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:46:20,498 - test_complete - INFO - 测试: 正则表达式测试
2025-07-26 09:46:20,499 - test_complete - INFO -   搜索词: 'User.*\d+', 模式: regex
2025-07-26 09:46:34,193 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:34,193 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:46:34,194 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:46:34,194 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:46:34,195 - test_complete - INFO -     结果 1: 'User ID: 12345' (分数: 1.000)
2025-07-26 09:46:34,195 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:46:34,196 - test_complete - INFO - 测试: 相似度匹配测试
2025-07-26 09:46:34,196 - test_complete - INFO -   搜索词: 'Button', 模式: similarity
2025-07-26 09:46:47,813 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:47,814 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:46:47,814 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:46:47,815 - test_complete - INFO -   找到 0 个结果 (预期: 2)
2025-07-26 09:46:47,815 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:46:47,816 - test_complete - INFO - 测试: 用户名搜索
2025-07-26 09:46:47,816 - test_complete - INFO -   搜索词: 'admin', 模式: contains
2025-07-26 09:47:02,137 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:47:02,138 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:47:02,139 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:47:02,139 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:47:02,140 - test_complete - INFO -     结果 1: 'Username: admin' (分数: 0.333)
2025-07-26 09:47:02,141 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:47:02,141 - test_complete - INFO - 测试: 邮箱搜索
2025-07-26 09:47:02,141 - test_complete - INFO -   搜索词: 'test@', 模式: contains
2025-07-26 09:47:17,557 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:47:17,558 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:47:17,559 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:47:17,559 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:47:17,560 - test_complete - INFO -     结果 1: 'Email: <EMAIL>' (分数: 0.217)
2025-07-26 09:47:17,561 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:47:17,561 - test_complete - INFO - 测试: 版本信息搜索
2025-07-26 09:47:17,562 - test_complete - INFO -   搜索词: 'Version', 模式: exact
2025-07-26 09:47:33,281 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:47:33,282 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:33,282 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:47:33,283 - test_complete - INFO -   找到 0 个结果 (预期: 1)
2025-07-26 09:47:33,284 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:47:33,351 - test_complete - INFO - === 测试批量搜索功能 ===
2025-07-26 09:47:34,270 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:47:34,271 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:47:49,267 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:47:49,268 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:49,268 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:47:49,269 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:49,270 - ocr - INFO - 搜索完成，找到4个匹配结果
2025-07-26 09:47:49,270 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:49,271 - ocr - INFO - 批量搜索完成，总共找到6个匹配结果
2025-07-26 09:47:49,272 - test_complete - INFO - 批量搜索完成，共 5 组结果
2025-07-26 09:47:49,273 - test_complete - INFO -   'Button': 0 个匹配
2025-07-26 09:47:49,274 - test_complete - INFO -   'User': 2 个匹配
2025-07-26 09:47:49,274 - test_complete - INFO -   'Status': 0 个匹配
2025-07-26 09:47:49,275 - test_complete - INFO -   '\d+': 4 个匹配
2025-07-26 09:47:49,276 - test_complete - INFO -   'Menu': 0 个匹配
2025-07-26 09:47:49,276 - test_complete - INFO - 总匹配数: 6
2025-07-26 09:47:49,345 - test_complete - INFO - === 测试可视化功能 ===
2025-07-26 09:47:50,272 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:47:50,273 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:48:05,220 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:48:05,221 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:48:05,222 - ocr - INFO - 高级搜索完成，找到2个匹配结果
2025-07-26 09:48:07,073 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:48:07,075 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:48:07,079 - test_complete - INFO - 标注图像已保存到 temp/annotated_comprehensive.png
2025-07-26 09:48:07,081 - visualization - ERROR - 创建结果覆盖层失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:48:07,084 - test_complete - INFO - 覆盖层图像已保存到 temp/overlay_comprehensive.png
2025-07-26 09:48:07,086 - test_complete - INFO - 结果摘要已保存到 temp/summary_comprehensive.png
2025-07-26 09:48:07,086 - test_complete - INFO - ✅ 可视化测试完成
2025-07-26 09:48:07,125 - test_complete - INFO - === 测试动作队列功能 ===
2025-07-26 09:48:07,125 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:48:07,126 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:48:07,126 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:48:08,009 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:48:08,010 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:48:22,750 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:48:22,751 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:48:22,751 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:48:22,752 - test_complete - WARNING - ⚠️ 没有搜索结果可用于动作队列测试
2025-07-26 09:48:22,812 - test_complete - INFO - === 测试搜索统计功能 ===
2025-07-26 09:48:23,703 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:48:23,704 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:48:39,162 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:48:39,163 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:48:39,164 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:48:55,352 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:48:55,353 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:48:55,354 - ocr - INFO - 高级搜索完成，找到2个匹配结果
2025-07-26 09:49:11,725 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:49:11,726 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:49:11,727 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:49:27,817 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:49:27,818 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:49:27,819 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:49:43,968 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:49:43,969 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:49:43,970 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:49:43,971 - test_complete - INFO - 搜索统计信息:
2025-07-26 09:49:43,971 - test_complete - INFO -   总结果数: 4
2025-07-26 09:49:43,972 - test_complete - INFO -   平均置信度: 0.979
2025-07-26 09:49:43,973 - test_complete - INFO -   平均匹配分数: 0.322
2025-07-26 09:49:43,973 - test_complete - INFO -   模式分布: {'contains': 4}
2025-07-26 09:49:43,974 - test_complete - INFO -   置信度分布: {'high': 4, 'medium': 0, 'low': 0}
2025-07-26 09:49:43,974 - test_complete - INFO -   最佳匹配: {'ocr_result': {'text': 'Status: Active', 'confidence': 0.9997528791427612, 'bbox': [[401.0, 221.0], [572.0, 221.0], [572.0, 245.0], [401.0, 245.0]], 'center': (486, 233), 'rect': (401.0, 221.0, 171.0, 24.0), 'area': 4104.0, 'region_id': 'region_2259840676336'}, 'match_score': 0.42857142857142855, 'match_text': 'Status: Active', 'search_term': 'Status', 'search_mode': 'contains'}
2025-07-26 09:49:43,975 - test_complete - INFO - ✅ 搜索统计测试完成
2025-07-26 09:49:44,047 - test_complete - INFO - ==================================================
2025-07-26 09:49:44,048 - test_complete - INFO - 完整功能测试总结:
2025-07-26 09:49:44,049 - test_complete - INFO - 通过测试: 4/5
2025-07-26 09:49:44,049 - test_complete - INFO -   search_modes: ✅ 通过
2025-07-26 09:49:44,050 - test_complete - INFO -   batch_search: ✅ 通过
2025-07-26 09:49:44,050 - test_complete - INFO -   visualization: ✅ 通过
2025-07-26 09:49:44,051 - test_complete - INFO -   action_queue: ❌ 失败
2025-07-26 09:49:44,051 - test_complete - INFO -   statistics: ✅ 通过
2025-07-26 09:49:44,052 - test_complete - WARNING - ⚠️ 1 个测试失败
2025-07-26 09:49:44,052 - test_complete - INFO - 测试完成，生成的文件:
2025-07-26 09:49:44,053 - test_complete - INFO -   - temp/comprehensive_test.png (测试图像)
2025-07-26 09:49:44,054 - test_complete - INFO -   - temp/annotated_comprehensive.png (标注图像)
2025-07-26 09:49:44,054 - test_complete - INFO -   - temp/overlay_comprehensive.png (覆盖层图像)
2025-07-26 09:49:44,055 - test_complete - INFO -   - temp/summary_comprehensive.png (结果摘要)
2025-07-26 09:56:35,651 - main - INFO - ==================================================
2025-07-26 09:56:35,652 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 09:56:35,652 - main - INFO - ==================================================
2025-07-26 09:56:36,909 - main - INFO - 所有依赖包检查通过
2025-07-26 09:56:36,910 - main - INFO - 目录结构检查完成
2025-07-26 09:56:36,910 - main - INFO - 启动GUI界面...
2025-07-26 09:56:37,034 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:56:37,891 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:56:37,892 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:56:37,892 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:56:37,892 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:56:37,893 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:56:37,894 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:56:37,921 - gui - INFO - 主窗口初始化完成
2025-07-26 09:57:38,473 - ocr - INFO - OCR识别完成，找到45个文本区域
2025-07-26 09:57:38,473 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:57:38,474 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:57:38,474 - gui - ERROR - 显示搜索结果失败: 'MainWindow' object has no attribute 'status_var'
2025-07-26 09:57:40,314 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 10:05:03,551 - main - INFO - ==================================================
2025-07-26 10:05:03,552 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:05:03,552 - main - INFO - ==================================================
2025-07-26 10:05:04,938 - main - INFO - 所有依赖包检查通过
2025-07-26 10:05:04,939 - main - INFO - 目录结构检查完成
2025-07-26 10:05:04,939 - main - INFO - 启动GUI界面...
2025-07-26 10:05:04,986 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:05:05,986 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:05:05,986 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:05:05,987 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:05:05,987 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:05:05,988 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:05:05,988 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:05:06,022 - gui - INFO - 主窗口初始化完成
2025-07-26 10:05:24,977 - action - INFO - 临时文件清理完成
2025-07-26 10:05:25,014 - main - INFO - 应用程序正常退出
2025-07-26 10:14:53,201 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:14:53,212 - test_critical_fixes - INFO - 开始关键问题修复验证测试...
2025-07-26 10:14:53,212 - test_critical_fixes - INFO - === 测试运行时错误修复 ===
2025-07-26 10:14:53,282 - test_critical_fixes - ERROR - ❌ 状态栏测试失败: MainWindow.__init__() takes 1 positional argument but 2 were given
2025-07-26 10:14:53,283 - test_critical_fixes - INFO - ✅ OpenCV坐标类型修复成功
2025-07-26 10:14:53,283 - test_critical_fixes - INFO - === 测试UI功能增强 ===
2025-07-26 10:14:53,284 - test_critical_fixes - INFO - ✅ 区域选择器创建成功
2025-07-26 10:14:53,286 - code_generator - INFO - 独立脚本已生成: generated_scripts\test_generated_script.py
2025-07-26 10:14:53,286 - test_critical_fixes - INFO - ✅ 代码生成器测试成功: generated_scripts\test_generated_script.py
2025-07-26 10:14:53,296 - test_critical_fixes - INFO - ✅ 图像导入功能测试成功
2025-07-26 10:14:53,297 - test_critical_fixes - INFO - === 测试性能优化 ===
2025-07-26 10:14:55,602 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:14:55,603 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:14:57,311 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 10:14:57,312 - test_critical_fixes - INFO - ✅ OCR缓存功能正常 (第一次: 1.708s, 第二次: 0.000s)
2025-07-26 10:14:57,313 - test_critical_fixes - INFO - 缓存信息: {'cache_size': 1, 'max_size': 100, 'cache_keys': ['af76ce17474be184_0.8']}
2025-07-26 10:14:57,314 - performance_monitor - INFO - 性能监控历史记录已清除
2025-07-26 10:14:57,421 - test_critical_fixes - INFO - ✅ 性能监控功能正常
2025-07-26 10:14:57,422 - test_critical_fixes - INFO - 性能摘要: {'total_operations': 1, 'time_range': {'start': 1753496097.4218936, 'end': 1753496097.4218936}, 'duration_stats': {'min': 0.10751152038574219, 'max': 0.10751152038574219, 'avg': 0.10751152038574219, 'total': 0.10751152038574219}, 'memory_stats': {'min': 0.00390625, 'max': 0.00390625, 'avg': 0.00390625, 'total': 0.00390625}, 'cpu_stats': {'min': 1376.1, 'max': 1376.1, 'avg': 1376.1}, 'operation_counts': {'test_operation': 1}}
2025-07-26 10:14:58,364 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:14:58,365 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:14:58,423 - test_critical_fixes - INFO - ✅ 图像预处理优化正常 (原始: (2000, 3000, 3), 处理后: (1280, 1920, 3))
2025-07-26 10:14:58,456 - test_critical_fixes - INFO - === 测试内存管理 ===
2025-07-26 10:15:00,458 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:15:00,459 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:15:02,546 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 10:15:04,796 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:06,930 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:08,990 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:11,124 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:13,255 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:15,514 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:17,806 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:20,079 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:22,210 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:22,254 - performance_monitor - INFO - 内存优化完成，回收对象: 742, 当前内存: 464.06MB
2025-07-26 10:15:23,281 - test_critical_fixes - INFO - 内存使用: 初始 291.41MB, 最终 464.11MB
2025-07-26 10:15:23,282 - test_critical_fixes - INFO - 内存优化结果: {'collected_objects': 742, 'current_memory_mb': 464.0625}
2025-07-26 10:15:23,283 - test_critical_fixes - INFO - ✅ 内存管理测试完成
2025-07-26 10:15:23,322 - test_critical_fixes - INFO - ============================================================
2025-07-26 10:15:23,322 - test_critical_fixes - INFO - 关键问题修复验证测试总结:
2025-07-26 10:15:23,323 - test_critical_fixes - INFO - 
RUNTIME_FIXES:
2025-07-26 10:15:23,323 - test_critical_fixes - INFO -   status_var_fix: ❌ 失败
2025-07-26 10:15:23,324 - test_critical_fixes - INFO -   opencv_coordinate_fix: ✅ 通过
2025-07-26 10:15:23,324 - test_critical_fixes - INFO - 
UI_ENHANCEMENTS:
2025-07-26 10:15:23,325 - test_critical_fixes - INFO -   region_selector: ✅ 通过
2025-07-26 10:15:23,325 - test_critical_fixes - INFO -   code_generator: ✅ 通过
2025-07-26 10:15:23,326 - test_critical_fixes - INFO -   image_import: ✅ 通过
2025-07-26 10:15:23,326 - test_critical_fixes - INFO - 
PERFORMANCE_OPTIMIZATIONS:
2025-07-26 10:15:23,326 - test_critical_fixes - INFO -   ocr_cache: ✅ 通过
2025-07-26 10:15:23,327 - test_critical_fixes - INFO -   performance_monitor: ✅ 通过
2025-07-26 10:15:23,327 - test_critical_fixes - INFO -   image_preprocessing: ✅ 通过
2025-07-26 10:15:23,327 - test_critical_fixes - INFO - 
MEMORY_MANAGEMENT:
2025-07-26 10:15:23,328 - test_critical_fixes - INFO -   memory_management: ✅ 通过
2025-07-26 10:15:23,328 - test_critical_fixes - INFO - 
总体结果: 8/9 测试通过
2025-07-26 10:15:23,328 - test_critical_fixes - WARNING - ⚠️ 1 个测试失败
2025-07-26 10:15:23,330 - performance_monitor - INFO - 性能指标已导出到: temp/performance_test_results.json
2025-07-26 10:15:23,330 - test_critical_fixes - INFO - 性能测试数据已导出到 temp/performance_test_results.json
2025-07-26 10:16:19,345 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:16:19,353 - test_critical_fixes - INFO - 开始关键问题修复验证测试...
2025-07-26 10:16:19,354 - test_critical_fixes - INFO - === 测试运行时错误修复 ===
2025-07-26 10:16:19,410 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:16:21,802 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:21,803 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:21,803 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:16:21,804 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:16:21,804 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:16:21,804 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:16:21,848 - gui - INFO - 主窗口初始化完成
2025-07-26 10:16:21,849 - test_critical_fixes - INFO - ✅ status_var属性修复成功
2025-07-26 10:16:21,884 - test_critical_fixes - INFO - ✅ OpenCV坐标类型修复成功
2025-07-26 10:16:21,885 - test_critical_fixes - INFO - === 测试UI功能增强 ===
2025-07-26 10:16:21,886 - test_critical_fixes - INFO - ✅ 区域选择器创建成功
2025-07-26 10:16:21,887 - code_generator - INFO - 独立脚本已生成: generated_scripts\test_generated_script.py
2025-07-26 10:16:21,888 - test_critical_fixes - INFO - ✅ 代码生成器测试成功: generated_scripts\test_generated_script.py
2025-07-26 10:16:21,891 - test_critical_fixes - INFO - ✅ 图像导入功能测试成功
2025-07-26 10:16:21,892 - test_critical_fixes - INFO - === 测试性能优化 ===
2025-07-26 10:16:22,883 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:22,883 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:24,488 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 10:16:24,489 - test_critical_fixes - INFO - ✅ OCR缓存功能正常 (第一次: 1.604s, 第二次: 0.001s)
2025-07-26 10:16:24,489 - test_critical_fixes - INFO - 缓存信息: {'cache_size': 1, 'max_size': 100, 'cache_keys': ['af76ce17474be184_0.8']}
2025-07-26 10:16:24,490 - performance_monitor - INFO - 性能监控历史记录已清除
2025-07-26 10:16:24,604 - test_critical_fixes - INFO - ✅ 性能监控功能正常
2025-07-26 10:16:24,604 - test_critical_fixes - INFO - 性能摘要: {'total_operations': 1, 'time_range': {'start': 1753496184.604868, 'end': 1753496184.604868}, 'duration_stats': {'min': 0.11462187767028809, 'max': 0.11462187767028809, 'avg': 0.11462187767028809, 'total': 0.11462187767028809}, 'memory_stats': {'min': 0.00390625, 'max': 0.00390625, 'avg': 0.00390625, 'total': 0.00390625}, 'cpu_stats': {'min': 1400.0, 'max': 1400.0, 'avg': 1400.0}, 'operation_counts': {'test_operation': 1}}
2025-07-26 10:16:25,575 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:25,575 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:25,633 - test_critical_fixes - INFO - ✅ 图像预处理优化正常 (原始: (2000, 3000, 3), 处理后: (1280, 1920, 3))
2025-07-26 10:16:25,666 - test_critical_fixes - INFO - === 测试内存管理 ===
2025-07-26 10:16:27,695 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:27,697 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:29,903 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:32,156 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:34,255 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:36,659 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:38,765 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:40,919 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:43,268 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:45,636 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:47,878 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:50,290 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:50,335 - performance_monitor - INFO - 内存优化完成，回收对象: 922, 当前内存: 561.18MB
2025-07-26 10:16:51,357 - test_critical_fixes - INFO - 内存使用: 初始 379.78MB, 最终 561.18MB
2025-07-26 10:16:51,357 - test_critical_fixes - INFO - 内存优化结果: {'collected_objects': 922, 'current_memory_mb': 561.17578125}
2025-07-26 10:16:51,359 - test_critical_fixes - INFO - ✅ 内存管理测试完成
2025-07-26 10:16:51,399 - test_critical_fixes - INFO - ============================================================
2025-07-26 10:16:51,400 - test_critical_fixes - INFO - 关键问题修复验证测试总结:
2025-07-26 10:16:51,401 - test_critical_fixes - INFO - 
RUNTIME_FIXES:
2025-07-26 10:16:51,401 - test_critical_fixes - INFO -   status_var_fix: ✅ 通过
2025-07-26 10:16:51,402 - test_critical_fixes - INFO -   opencv_coordinate_fix: ✅ 通过
2025-07-26 10:16:51,402 - test_critical_fixes - INFO - 
UI_ENHANCEMENTS:
2025-07-26 10:16:51,403 - test_critical_fixes - INFO -   region_selector: ✅ 通过
2025-07-26 10:16:51,403 - test_critical_fixes - INFO -   code_generator: ✅ 通过
2025-07-26 10:16:51,403 - test_critical_fixes - INFO -   image_import: ✅ 通过
2025-07-26 10:16:51,404 - test_critical_fixes - INFO - 
PERFORMANCE_OPTIMIZATIONS:
2025-07-26 10:16:51,404 - test_critical_fixes - INFO -   ocr_cache: ✅ 通过
2025-07-26 10:16:51,404 - test_critical_fixes - INFO -   performance_monitor: ✅ 通过
2025-07-26 10:16:51,405 - test_critical_fixes - INFO -   image_preprocessing: ✅ 通过
2025-07-26 10:16:51,405 - test_critical_fixes - INFO - 
MEMORY_MANAGEMENT:
2025-07-26 10:16:51,406 - test_critical_fixes - INFO -   memory_management: ✅ 通过
2025-07-26 10:16:51,406 - test_critical_fixes - INFO - 
总体结果: 9/9 测试通过
2025-07-26 10:16:51,407 - test_critical_fixes - INFO - 🎉 所有关键问题修复验证测试通过！
2025-07-26 10:16:51,409 - performance_monitor - INFO - 性能指标已导出到: temp/performance_test_results.json
2025-07-26 10:16:51,409 - test_critical_fixes - INFO - 性能测试数据已导出到 temp/performance_test_results.json
2025-07-26 10:17:09,378 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:17:09,388 - main - INFO - ==================================================
2025-07-26 10:17:09,388 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:17:09,389 - main - INFO - ==================================================
2025-07-26 10:17:10,812 - main - INFO - 所有依赖包检查通过
2025-07-26 10:17:10,813 - main - INFO - 目录结构检查完成
2025-07-26 10:17:10,814 - main - INFO - 启动GUI界面...
2025-07-26 10:17:10,860 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:17:11,794 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:17:11,795 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:17:11,796 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:17:11,796 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:17:11,797 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:17:11,797 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:17:11,833 - gui - INFO - 主窗口初始化完成
2025-07-26 10:17:19,952 - region_selector - INFO - 开始区域选择
2025-07-26 10:17:20,582 - region_selector - INFO - 区域选择窗口已创建
2025-07-26 10:17:22,448 - region_selector - INFO - 选择区域: (574, 328, 30, 26)
2025-07-26 10:17:24,807 - region_selector - INFO - 确认选择区域: (574, 328, 30, 26)
2025-07-26 10:17:24,809 - gui - ERROR - 区域截图回调失败: 'ScreenCapture' object has no attribute 'capture_region'
2025-07-26 10:17:27,863 - region_selector - WARNING - 选择区域太小，请重新选择
2025-07-26 10:17:29,442 - region_selector - INFO - 确认选择区域: (986, 514, 0, 0)
2025-07-26 10:17:29,443 - gui - ERROR - 区域截图回调失败: 'ScreenCapture' object has no attribute 'capture_region'
2025-07-26 10:17:30,714 - region_selector - WARNING - 选择区域太小，请重新选择
2025-07-26 10:17:32,038 - region_selector - INFO - 用户取消区域选择
2025-07-26 10:17:32,078 - region_selector - INFO - 区域选择器已清理
2025-07-26 10:17:36,021 - region_selector - INFO - 区域选择器已清理
2025-07-26 10:17:37,973 - region_selector - INFO - 区域选择器已清理
2025-07-26 10:18:02,843 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:18:02,854 - main - INFO - ==================================================
2025-07-26 10:18:02,854 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:18:02,855 - main - INFO - ==================================================
2025-07-26 10:18:04,218 - main - INFO - 所有依赖包检查通过
2025-07-26 10:18:04,219 - main - INFO - 目录结构检查完成
2025-07-26 10:18:04,220 - main - INFO - 启动GUI界面...
2025-07-26 10:18:04,273 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:18:05,208 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:18:05,209 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:18:05,209 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:18:05,210 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:18:05,211 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:18:05,211 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:18:05,249 - gui - INFO - 主窗口初始化完成
2025-07-26 10:46:39,357 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:46:39,386 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:47:21,846 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:47:21,859 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:47:21,863 - ux_test - INFO - 开始用户体验测试...
2025-07-26 10:47:21,868 - ux_test - INFO - 测试图像创建完成
2025-07-26 10:47:21,917 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:24,379 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:47:24,380 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:47:24,380 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:24,381 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:24,381 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:24,382 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:47:24,385 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 10:47:24,386 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 10:47:24,394 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 10:47:24,395 - ux_test - ERROR - 创建主窗口失败: 'MainWindow' object has no attribute 'batch_click_search_results'
2025-07-26 10:47:46,647 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:47:46,661 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:47:46,665 - ux_test - INFO - 开始用户体验测试...
2025-07-26 10:47:46,670 - ux_test - INFO - 测试图像创建完成
2025-07-26 10:47:46,724 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:49,140 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:47:49,141 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:47:49,141 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:49,142 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:49,142 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:49,142 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:47:49,144 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 10:47:49,145 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 10:47:49,154 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 10:47:49,160 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,161 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,162 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,163 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,166 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,168 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,169 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,171 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,173 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,174 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,175 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,175 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,178 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,178 - gui - INFO - 增强工具栏创建完成
2025-07-26 10:47:49,196 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 10:47:49,200 - gui - INFO - 主窗口初始化完成
2025-07-26 10:47:49,201 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 10:47:49,202 - ux_test - INFO - 主窗口创建完成
2025-07-26 10:47:49,203 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 10:47:49,381 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 10:47:49,382 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 10:47:49,383 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 10:47:49,383 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 10:47:49,384 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 10:47:49,384 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 10:47:49,441 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 10:47:49,441 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 10:47:49,442 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 10:47:49,444 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 10:47:49,445 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 10:47:49,445 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 10:47:49,446 - ux_test - INFO - 帮助系统测试完成
2025-07-26 10:47:49,446 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 10:47:49,446 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 10:47:49,460 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 10:47:49,462 - ux_test - INFO - 响应性能测试完成
2025-07-26 10:47:49,462 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 10:47:49,463 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 10:47:49,463 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 10:47:49,464 - ux_test - INFO - 错误处理测试完成
2025-07-26 10:47:49,464 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 10:47:49,464 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 10:47:49,465 - ux_test - INFO - 可访问性测试完成
2025-07-26 10:47:49,465 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 10:47:49,466 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 10:47:49,538 - ux_test - INFO - 测试资源清理完成
2025-07-26 10:49:53,479 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:49:53,492 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:49:53,495 - main - INFO - ==================================================
2025-07-26 10:49:53,496 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:49:53,496 - main - INFO - ==================================================
2025-07-26 10:49:54,861 - main - INFO - 所有依赖包检查通过
2025-07-26 10:49:54,862 - main - INFO - 目录结构检查完成
2025-07-26 10:49:54,862 - main - INFO - 启动GUI界面...
2025-07-26 10:49:54,909 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:49:56,298 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:49:56,299 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:49:56,299 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:49:56,300 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:49:56,300 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:49:56,300 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:49:56,302 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 10:49:56,303 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 10:49:56,317 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 10:49:56,323 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,324 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,325 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,327 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,328 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,329 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,330 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,333 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,334 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,338 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,340 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,343 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,347 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,348 - gui - INFO - 增强工具栏创建完成
2025-07-26 10:49:56,378 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 10:49:56,382 - gui - INFO - 主窗口初始化完成
2025-07-26 11:03:06,101 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:03:06,118 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:03:06,122 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:03:06,128 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:03:06,181 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:03:08,822 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:03:08,823 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:03:08,824 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:03:08,825 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:03:08,825 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:03:08,826 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:03:08,827 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:03:08,828 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:03:08,840 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:03:08,848 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,850 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,851 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,852 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,855 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,857 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,859 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,861 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,862 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,864 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,865 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,867 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,868 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,869 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:03:08,893 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:03:08,897 - gui - INFO - 主窗口初始化完成
2025-07-26 11:03:08,898 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:03:08,899 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:03:08,899 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:03:09,022 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:03:09,022 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:03:09,023 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:03:09,023 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:03:09,024 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:03:09,024 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:03:09,066 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:03:09,067 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:03:09,067 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:03:09,069 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:03:09,070 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:03:09,070 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:03:09,071 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:03:09,071 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:03:09,071 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:03:09,082 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:03:09,082 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:03:09,083 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:03:09,083 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:03:09,084 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:03:09,084 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:03:09,085 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:03:09,085 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:03:09,086 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:03:09,086 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:03:09,087 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:03:09,133 - ux_test - INFO - 测试资源清理完成
