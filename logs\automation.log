2025-07-26 07:50:26,901 - main - INFO - ==================================================
2025-07-26 07:50:26,905 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 07:50:26,906 - main - INFO - ==================================================
2025-07-26 07:50:28,178 - main - INFO - 所有依赖包检查通过
2025-07-26 07:50:28,183 - main - INFO - 目录结构检查完成
2025-07-26 07:50:28,183 - main - INFO - 启动GUI界面...
2025-07-26 07:50:28,622 - screen - INFO - ScreenCapture初始化完成
2025-07-26 07:50:29,719 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 07:50:29,720 - ocr - INFO - OCREngine初始化完成
2025-07-26 07:50:29,721 - match - INFO - TemplateMatch初始化完成
2025-07-26 07:50:29,721 - screen - INFO - ScreenCapture初始化完成
2025-07-26 07:50:29,722 - match - INFO - TemplateMatch初始化完成
2025-07-26 07:50:29,723 - action - INFO - ActionExecutor初始化完成
2025-07-26 07:50:29,767 - gui - INFO - 主窗口初始化完成
2025-07-26 07:51:17,011 - ocr - INFO - OCR识别完成，找到39个文本区域
2025-07-26 07:51:31,611 - action - INFO - 临时文件清理完成
2025-07-26 07:51:31,655 - main - INFO - 应用程序正常退出
2025-07-26 08:29:22,144 - main - INFO - ==================================================
2025-07-26 08:29:22,145 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 08:29:22,145 - main - INFO - ==================================================
2025-07-26 08:29:23,606 - main - INFO - 所有依赖包检查通过
2025-07-26 08:29:23,607 - main - INFO - 目录结构检查完成
2025-07-26 08:29:23,608 - main - INFO - 启动GUI界面...
2025-07-26 08:29:24,093 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:29:25,488 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 08:29:25,489 - ocr - INFO - OCREngine初始化完成
2025-07-26 08:29:25,490 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:29:25,491 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:29:25,492 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:29:25,493 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:29:25,579 - gui - INFO - 主窗口初始化完成
2025-07-26 08:30:05,970 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,972 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,972 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,976 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,978 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,980 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,980 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,980 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,981 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:05,983 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:05,983 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:05,983 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:06,089 - action - INFO - 点击坐标: (102, 201), 按钮: left
2025-07-26 08:30:06,090 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:06,091 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:06,091 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:06,093 - action - INFO - 按键: enter, 次数: 1
2025-07-26 08:30:06,094 - screen - INFO - ScreenCapture初始化完成
2025-07-26 08:30:06,095 - match - INFO - TemplateMatch初始化完成
2025-07-26 08:30:06,096 - action - INFO - ActionExecutor初始化完成
2025-07-26 08:30:06,097 - action - INFO - 输入文本: '测试文本'
2025-07-26 09:36:05,229 - main - INFO - ==================================================
2025-07-26 09:36:05,229 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 09:36:05,230 - main - INFO - ==================================================
2025-07-26 09:36:10,428 - main - INFO - 所有依赖包检查通过
2025-07-26 09:36:10,429 - main - INFO - 目录结构检查完成
2025-07-26 09:36:10,429 - main - INFO - 启动GUI界面...
2025-07-26 09:36:10,893 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:36:11,969 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:36:11,970 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:36:11,971 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:36:11,971 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:36:11,971 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:36:11,972 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:36:12,074 - gui - INFO - 主窗口初始化完成
2025-07-26 09:37:28,246 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:37:28,248 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:37:28,250 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:37:28,251 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:37:28,252 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:37:28,254 - test_ocr_search - INFO - 开始测试OCR搜索功能
2025-07-26 09:37:28,298 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:37:28,299 - test_ocr_search - INFO - 测试1: 基本OCR识别
2025-07-26 09:37:57,590 - ocr - INFO - OCR识别完成，找到78个文本区域
2025-07-26 09:37:57,621 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:37:57,623 - ocr - INFO - 高级搜索完成，找到2个匹配结果
2025-07-26 09:37:57,625 - gui - ERROR - 显示搜索结果失败: 'MainWindow' object has no attribute 'status_var'
2025-07-26 09:37:59,225 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:37:59,245 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:38:02,259 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:38:02,260 - test_ocr_search - INFO - 识别到2个文本区域
2025-07-26 09:38:02,260 - test_ocr_search - INFO -   文本: 'Screen Recoqnition', 置信度: 0.979, 中心: (420, 142)
2025-07-26 09:38:02,261 - test_ocr_search - INFO -   文本: 'Test', 置信度: 0.803, 中心: (101, 186)
2025-07-26 09:38:02,261 - test_ocr_search - INFO - 测试2: 精确搜索
2025-07-26 09:38:11,248 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:11,249 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:11,249 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:11,250 - test_ocr_search - INFO - 精确搜索找到1个结果
2025-07-26 09:38:11,250 - test_ocr_search - INFO - 测试3: 包含搜索
2025-07-26 09:38:18,474 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:18,475 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:18,476 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:18,476 - test_ocr_search - INFO - 包含搜索找到1个结果
2025-07-26 09:38:18,477 - test_ocr_search - INFO - 测试4: 模糊搜索
2025-07-26 09:38:22,951 - action - INFO - 点击坐标: (1271, 726), 按钮: left
2025-07-26 09:38:22,960 - action - INFO - 成功点击OCR结果: '坐标' at (1269, 726)
2025-07-26 09:38:22,971 - action - INFO - 成功点击搜索结果: '坐标' -> '坐标'
2025-07-26 09:38:22,976 - action - INFO - 批量点击 1: 坐标
2025-07-26 09:38:24,203 - action - INFO - 点击坐标: (231, 264), 按钮: left
2025-07-26 09:38:24,214 - action - INFO - 成功点击OCR结果: '搜索文本：坐标' at (233, 264)
2025-07-26 09:38:24,216 - action - INFO - 成功点击搜索结果: '坐标' -> '搜索文本：坐标'
2025-07-26 09:38:24,217 - action - INFO - 批量点击 2: 搜索文本：坐标
2025-07-26 09:38:24,217 - action - INFO - 批量点击完成，成功点击2个结果
2025-07-26 09:38:25,672 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:25,673 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:25,674 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:25,674 - test_ocr_search - INFO - 模糊搜索找到1个结果
2025-07-26 09:38:25,675 - test_ocr_search - INFO - 测试5: 批量搜索
2025-07-26 09:38:33,229 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:33,230 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:33,231 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:33,232 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:33,233 - ocr - INFO - 批量搜索完成，总共找到3个匹配结果
2025-07-26 09:38:33,233 - test_ocr_search - INFO - 批量搜索完成，找到3组结果
2025-07-26 09:38:33,234 - test_ocr_search - INFO - 测试6: 图像标注
2025-07-26 09:38:33,480 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:38:33,482 - test_ocr_search - INFO - 标注图像已保存到 temp/test_annotated.png
2025-07-26 09:38:33,483 - test_ocr_search - INFO - 测试7: 搜索统计
2025-07-26 09:38:33,483 - test_ocr_search - INFO - 搜索统计: {'total_results': 1, 'average_confidence': 0.9983128309249878, 'average_match_score': 0.8888888888888888, 'mode_distribution': {'fuzzy': 1}, 'confidence_distribution': {'high': 1, 'medium': 0, 'low': 0}, 'best_match': {'ocr_result': {'text': 'Hello', 'confidence': 0.9983128309249878, 'bbox': [[53.0, 54.0], [249.0, 58.0], [248.0, 105.0], [52.0, 102.0]], 'center': (150, 79), 'rect': (52.0, 54.0, 197.0, 51.0), 'area': 10047.0, 'region_id': 'region_2480628010288'}, 'match_score': 0.8888888888888888, 'match_text': 'Hello', 'search_term': 'Helo', 'search_mode': 'fuzzy'}}
2025-07-26 09:38:33,484 - test_ocr_search - INFO - 测试8: 动作队列
2025-07-26 09:38:33,484 - test_ocr_search - INFO - 动作队列创建完成，包含2个动作
2025-07-26 09:38:33,485 - test_ocr_search - INFO - 所有测试完成！
2025-07-26 09:38:34,465 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:38:34,466 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:38:34,470 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:38:34,470 - test_ocr_search - INFO - 测试搜索模式: exact, 搜索词: 'Hello World'
2025-07-26 09:38:41,551 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:38:41,552 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:38:41,553 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:38:41,554 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:38:41,554 - test_ocr_search - INFO - 测试搜索模式: contains, 搜索词: 'World'
2025-07-26 09:38:48,725 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:48,726 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:38:48,727 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:38:48,728 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:38:48,728 - test_ocr_search - INFO -     匹配: 'World', 分数: 1.000
2025-07-26 09:38:48,729 - test_ocr_search - INFO - 测试搜索模式: fuzzy, 搜索词: 'Helo World'
2025-07-26 09:38:58,815 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:38:58,816 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:38:58,816 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:38:58,817 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:38:58,817 - test_ocr_search - INFO - 测试搜索模式: similarity, 搜索词: 'Hello'
2025-07-26 09:39:06,660 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:06,661 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:06,661 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:06,662 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:39:06,663 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:39:06,663 - test_ocr_search - INFO - 测试搜索模式: regex, 搜索词: 'Hello.*'
2025-07-26 09:39:14,810 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:14,811 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:14,812 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:14,812 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:39:14,813 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:39:18,100 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:39:18,100 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:39:18,101 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:39:18,101 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:39:18,102 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:39:18,102 - test_ocr_search - INFO - 开始测试OCR搜索功能
2025-07-26 09:39:18,105 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:39:18,105 - test_ocr_search - INFO - 测试1: 基本OCR识别
2025-07-26 09:39:25,360 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:39:25,360 - test_ocr_search - INFO - 识别到2个文本区域
2025-07-26 09:39:25,361 - test_ocr_search - INFO -   文本: 'Screen Recoqnition', 置信度: 0.979, 中心: (420, 142)
2025-07-26 09:39:25,361 - test_ocr_search - INFO -   文本: 'Test', 置信度: 0.803, 中心: (101, 186)
2025-07-26 09:39:25,362 - test_ocr_search - INFO - 测试2: 精确搜索
2025-07-26 09:39:32,340 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:32,341 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:32,342 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:32,342 - test_ocr_search - INFO - 精确搜索找到1个结果
2025-07-26 09:39:32,343 - test_ocr_search - INFO - 测试3: 包含搜索
2025-07-26 09:39:39,698 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:39,699 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:39,700 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:39,701 - test_ocr_search - INFO - 包含搜索找到1个结果
2025-07-26 09:39:39,701 - test_ocr_search - INFO - 测试4: 模糊搜索
2025-07-26 09:39:47,392 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:47,393 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:47,393 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:39:47,395 - test_ocr_search - INFO - 模糊搜索找到1个结果
2025-07-26 09:39:47,396 - test_ocr_search - INFO - 测试5: 批量搜索
2025-07-26 09:39:55,389 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:39:55,390 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:55,391 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:55,392 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:39:55,393 - ocr - INFO - 批量搜索完成，总共找到3个匹配结果
2025-07-26 09:39:55,394 - test_ocr_search - INFO - 批量搜索完成，找到3组结果
2025-07-26 09:39:55,394 - test_ocr_search - INFO - 测试6: 图像标注
2025-07-26 09:39:55,721 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:39:55,725 - test_ocr_search - INFO - 标注图像已保存到 temp/test_annotated.png
2025-07-26 09:39:55,726 - test_ocr_search - INFO - 测试7: 搜索统计
2025-07-26 09:39:55,728 - test_ocr_search - INFO - 搜索统计: {'total_results': 1, 'average_confidence': 0.9983128309249878, 'average_match_score': 0.8888888888888888, 'mode_distribution': {'fuzzy': 1}, 'confidence_distribution': {'high': 1, 'medium': 0, 'low': 0}, 'best_match': {'ocr_result': {'text': 'Hello', 'confidence': 0.9983128309249878, 'bbox': [[53.0, 54.0], [249.0, 58.0], [248.0, 105.0], [52.0, 102.0]], 'center': (150, 79), 'rect': (52.0, 54.0, 197.0, 51.0), 'area': 10047.0, 'region_id': 'region_2952611990832'}, 'match_score': 0.8888888888888888, 'match_text': 'Hello', 'search_term': 'Helo', 'search_mode': 'fuzzy'}}
2025-07-26 09:39:55,729 - test_ocr_search - INFO - 测试8: 动作队列
2025-07-26 09:39:55,730 - test_ocr_search - INFO - 动作队列创建完成，包含2个动作
2025-07-26 09:39:55,731 - test_ocr_search - INFO - 所有测试完成！
2025-07-26 09:39:56,812 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:39:56,813 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:39:56,816 - test_ocr_search - INFO - 测试图像已创建并保存到 temp/test_image.png
2025-07-26 09:39:56,816 - test_ocr_search - INFO - 测试搜索模式: exact, 搜索词: 'Hello World'
2025-07-26 09:40:04,653 - ocr - INFO - OCR识别完成，找到2个文本区域
2025-07-26 09:40:04,654 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:40:04,655 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:40:04,656 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:40:04,656 - test_ocr_search - INFO - 测试搜索模式: contains, 搜索词: 'World'
2025-07-26 09:40:11,812 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:11,813 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:40:11,814 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:40:11,815 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:40:11,815 - test_ocr_search - INFO -     匹配: 'World', 分数: 1.000
2025-07-26 09:40:11,816 - test_ocr_search - INFO - 测试搜索模式: fuzzy, 搜索词: 'Helo World'
2025-07-26 09:40:20,164 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:20,166 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:40:20,167 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:40:20,167 - test_ocr_search - INFO -   找到0个结果
2025-07-26 09:40:20,169 - test_ocr_search - INFO - 测试搜索模式: similarity, 搜索词: 'Hello'
2025-07-26 09:40:27,459 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:27,460 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:40:27,461 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:40:27,462 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:40:27,462 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:40:27,463 - test_ocr_search - INFO - 测试搜索模式: regex, 搜索词: 'Hello.*'
2025-07-26 09:40:34,536 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 09:40:34,537 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:40:34,538 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:40:34,538 - test_ocr_search - INFO -   找到1个结果
2025-07-26 09:40:34,539 - test_ocr_search - INFO -     匹配: 'Hello', 分数: 1.000
2025-07-26 09:44:08,643 - main - INFO - ==================================================
2025-07-26 09:44:08,643 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 09:44:08,643 - main - INFO - ==================================================
2025-07-26 09:44:09,968 - main - INFO - 所有依赖包检查通过
2025-07-26 09:44:09,969 - main - INFO - 目录结构检查完成
2025-07-26 09:44:09,969 - main - INFO - 启动GUI界面...
2025-07-26 09:44:10,016 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:44:10,970 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:44:10,970 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:44:10,971 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:44:10,971 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:44:10,972 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:44:10,973 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:44:11,006 - gui - INFO - 主窗口初始化完成
2025-07-26 09:45:38,368 - test_complete - INFO - 开始完整功能测试...
2025-07-26 09:45:38,369 - test_complete - INFO - === 测试所有搜索模式 ===
2025-07-26 09:45:40,502 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:45:40,503 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:45:40,510 - test_complete - INFO - 综合测试图像已保存
2025-07-26 09:45:40,510 - test_complete - INFO - 测试: 精确匹配测试
2025-07-26 09:45:40,511 - test_complete - INFO -   搜索词: 'Login Button', 模式: exact
2025-07-26 09:45:53,620 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:45:53,621 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:45:53,621 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:45:53,622 - test_complete - INFO -   找到 0 个结果 (预期: 1)
2025-07-26 09:45:53,622 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:45:53,623 - test_complete - INFO - 测试: 包含匹配测试（忽略大小写）
2025-07-26 09:45:53,623 - test_complete - INFO -   搜索词: 'login', 模式: contains
2025-07-26 09:46:06,918 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:06,918 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:46:06,919 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:46:06,919 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:46:06,920 - test_complete - INFO -     结果 1: 'Login Button' (分数: 0.417)
2025-07-26 09:46:06,920 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:46:06,921 - test_complete - INFO - 测试: 模糊匹配测试（拼写错误）
2025-07-26 09:46:06,921 - test_complete - INFO -   搜索词: 'Logn', 模式: fuzzy
2025-07-26 09:46:20,495 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:20,496 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:46:20,497 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:46:20,497 - test_complete - INFO -   找到 0 个结果 (预期: 1)
2025-07-26 09:46:20,498 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:46:20,498 - test_complete - INFO - 测试: 正则表达式测试
2025-07-26 09:46:20,499 - test_complete - INFO -   搜索词: 'User.*\d+', 模式: regex
2025-07-26 09:46:34,193 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:34,193 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:46:34,194 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:46:34,194 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:46:34,195 - test_complete - INFO -     结果 1: 'User ID: 12345' (分数: 1.000)
2025-07-26 09:46:34,195 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:46:34,196 - test_complete - INFO - 测试: 相似度匹配测试
2025-07-26 09:46:34,196 - test_complete - INFO -   搜索词: 'Button', 模式: similarity
2025-07-26 09:46:47,813 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:46:47,814 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:46:47,814 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:46:47,815 - test_complete - INFO -   找到 0 个结果 (预期: 2)
2025-07-26 09:46:47,815 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:46:47,816 - test_complete - INFO - 测试: 用户名搜索
2025-07-26 09:46:47,816 - test_complete - INFO -   搜索词: 'admin', 模式: contains
2025-07-26 09:47:02,137 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:47:02,138 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:47:02,139 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:47:02,139 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:47:02,140 - test_complete - INFO -     结果 1: 'Username: admin' (分数: 0.333)
2025-07-26 09:47:02,141 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:47:02,141 - test_complete - INFO - 测试: 邮箱搜索
2025-07-26 09:47:02,141 - test_complete - INFO -   搜索词: 'test@', 模式: contains
2025-07-26 09:47:17,557 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:47:17,558 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:47:17,559 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:47:17,559 - test_complete - INFO -   找到 1 个结果 (预期: 1)
2025-07-26 09:47:17,560 - test_complete - INFO -     结果 1: 'Email: <EMAIL>' (分数: 0.217)
2025-07-26 09:47:17,561 - test_complete - INFO -   ✅ 测试通过
2025-07-26 09:47:17,561 - test_complete - INFO - 测试: 版本信息搜索
2025-07-26 09:47:17,562 - test_complete - INFO -   搜索词: 'Version', 模式: exact
2025-07-26 09:47:33,281 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:47:33,282 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:33,282 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:47:33,283 - test_complete - INFO -   找到 0 个结果 (预期: 1)
2025-07-26 09:47:33,284 - test_complete - WARNING -   ⚠️ 结果数量少于预期
2025-07-26 09:47:33,351 - test_complete - INFO - === 测试批量搜索功能 ===
2025-07-26 09:47:34,270 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:47:34,271 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:47:49,267 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:47:49,268 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:49,268 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:47:49,269 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:49,270 - ocr - INFO - 搜索完成，找到4个匹配结果
2025-07-26 09:47:49,270 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:47:49,271 - ocr - INFO - 批量搜索完成，总共找到6个匹配结果
2025-07-26 09:47:49,272 - test_complete - INFO - 批量搜索完成，共 5 组结果
2025-07-26 09:47:49,273 - test_complete - INFO -   'Button': 0 个匹配
2025-07-26 09:47:49,274 - test_complete - INFO -   'User': 2 个匹配
2025-07-26 09:47:49,274 - test_complete - INFO -   'Status': 0 个匹配
2025-07-26 09:47:49,275 - test_complete - INFO -   '\d+': 4 个匹配
2025-07-26 09:47:49,276 - test_complete - INFO -   'Menu': 0 个匹配
2025-07-26 09:47:49,276 - test_complete - INFO - 总匹配数: 6
2025-07-26 09:47:49,345 - test_complete - INFO - === 测试可视化功能 ===
2025-07-26 09:47:50,272 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:47:50,273 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:48:05,220 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:48:05,221 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:48:05,222 - ocr - INFO - 高级搜索完成，找到2个匹配结果
2025-07-26 09:48:07,073 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:48:07,075 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:48:07,079 - test_complete - INFO - 标注图像已保存到 temp/annotated_comprehensive.png
2025-07-26 09:48:07,081 - visualization - ERROR - 创建结果覆盖层失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 09:48:07,084 - test_complete - INFO - 覆盖层图像已保存到 temp/overlay_comprehensive.png
2025-07-26 09:48:07,086 - test_complete - INFO - 结果摘要已保存到 temp/summary_comprehensive.png
2025-07-26 09:48:07,086 - test_complete - INFO - ✅ 可视化测试完成
2025-07-26 09:48:07,125 - test_complete - INFO - === 测试动作队列功能 ===
2025-07-26 09:48:07,125 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:48:07,126 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:48:07,126 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:48:08,009 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:48:08,010 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:48:22,750 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:48:22,751 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:48:22,751 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:48:22,752 - test_complete - WARNING - ⚠️ 没有搜索结果可用于动作队列测试
2025-07-26 09:48:22,812 - test_complete - INFO - === 测试搜索统计功能 ===
2025-07-26 09:48:23,703 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:48:23,704 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:48:39,162 - ocr - INFO - OCR识别完成，找到15个文本区域
2025-07-26 09:48:39,163 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:48:39,164 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:48:55,352 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:48:55,353 - ocr - INFO - 搜索完成，找到2个匹配结果
2025-07-26 09:48:55,354 - ocr - INFO - 高级搜索完成，找到2个匹配结果
2025-07-26 09:49:11,725 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:49:11,726 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:49:11,727 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:49:27,817 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:49:27,818 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 09:49:27,819 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 09:49:43,968 - ocr - INFO - OCR识别完成，找到16个文本区域
2025-07-26 09:49:43,969 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:49:43,970 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:49:43,971 - test_complete - INFO - 搜索统计信息:
2025-07-26 09:49:43,971 - test_complete - INFO -   总结果数: 4
2025-07-26 09:49:43,972 - test_complete - INFO -   平均置信度: 0.979
2025-07-26 09:49:43,973 - test_complete - INFO -   平均匹配分数: 0.322
2025-07-26 09:49:43,973 - test_complete - INFO -   模式分布: {'contains': 4}
2025-07-26 09:49:43,974 - test_complete - INFO -   置信度分布: {'high': 4, 'medium': 0, 'low': 0}
2025-07-26 09:49:43,974 - test_complete - INFO -   最佳匹配: {'ocr_result': {'text': 'Status: Active', 'confidence': 0.9997528791427612, 'bbox': [[401.0, 221.0], [572.0, 221.0], [572.0, 245.0], [401.0, 245.0]], 'center': (486, 233), 'rect': (401.0, 221.0, 171.0, 24.0), 'area': 4104.0, 'region_id': 'region_2259840676336'}, 'match_score': 0.42857142857142855, 'match_text': 'Status: Active', 'search_term': 'Status', 'search_mode': 'contains'}
2025-07-26 09:49:43,975 - test_complete - INFO - ✅ 搜索统计测试完成
2025-07-26 09:49:44,047 - test_complete - INFO - ==================================================
2025-07-26 09:49:44,048 - test_complete - INFO - 完整功能测试总结:
2025-07-26 09:49:44,049 - test_complete - INFO - 通过测试: 4/5
2025-07-26 09:49:44,049 - test_complete - INFO -   search_modes: ✅ 通过
2025-07-26 09:49:44,050 - test_complete - INFO -   batch_search: ✅ 通过
2025-07-26 09:49:44,050 - test_complete - INFO -   visualization: ✅ 通过
2025-07-26 09:49:44,051 - test_complete - INFO -   action_queue: ❌ 失败
2025-07-26 09:49:44,051 - test_complete - INFO -   statistics: ✅ 通过
2025-07-26 09:49:44,052 - test_complete - WARNING - ⚠️ 1 个测试失败
2025-07-26 09:49:44,052 - test_complete - INFO - 测试完成，生成的文件:
2025-07-26 09:49:44,053 - test_complete - INFO -   - temp/comprehensive_test.png (测试图像)
2025-07-26 09:49:44,054 - test_complete - INFO -   - temp/annotated_comprehensive.png (标注图像)
2025-07-26 09:49:44,054 - test_complete - INFO -   - temp/overlay_comprehensive.png (覆盖层图像)
2025-07-26 09:49:44,055 - test_complete - INFO -   - temp/summary_comprehensive.png (结果摘要)
2025-07-26 09:56:35,651 - main - INFO - ==================================================
2025-07-26 09:56:35,652 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 09:56:35,652 - main - INFO - ==================================================
2025-07-26 09:56:36,909 - main - INFO - 所有依赖包检查通过
2025-07-26 09:56:36,910 - main - INFO - 目录结构检查完成
2025-07-26 09:56:36,910 - main - INFO - 启动GUI界面...
2025-07-26 09:56:37,034 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:56:37,891 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 09:56:37,892 - ocr - INFO - OCREngine初始化完成
2025-07-26 09:56:37,892 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:56:37,892 - screen - INFO - ScreenCapture初始化完成
2025-07-26 09:56:37,893 - match - INFO - TemplateMatch初始化完成
2025-07-26 09:56:37,894 - action - INFO - ActionExecutor初始化完成
2025-07-26 09:56:37,921 - gui - INFO - 主窗口初始化完成
2025-07-26 09:57:38,473 - ocr - INFO - OCR识别完成，找到45个文本区域
2025-07-26 09:57:38,473 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 09:57:38,474 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 09:57:38,474 - gui - ERROR - 显示搜索结果失败: 'MainWindow' object has no attribute 'status_var'
2025-07-26 09:57:40,314 - visualization - WARNING - 绘制搜索信息失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'rectangle'
> Overload resolution failed:
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'pt1'. Sequence item with index 0 has a wrong type
>  - Can't parse 'rec'. Expected sequence length 4, got 2
>  - Can't parse 'rec'. Expected sequence length 4, got 2

2025-07-26 10:05:03,551 - main - INFO - ==================================================
2025-07-26 10:05:03,552 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:05:03,552 - main - INFO - ==================================================
2025-07-26 10:05:04,938 - main - INFO - 所有依赖包检查通过
2025-07-26 10:05:04,939 - main - INFO - 目录结构检查完成
2025-07-26 10:05:04,939 - main - INFO - 启动GUI界面...
2025-07-26 10:05:04,986 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:05:05,986 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:05:05,986 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:05:05,987 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:05:05,987 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:05:05,988 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:05:05,988 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:05:06,022 - gui - INFO - 主窗口初始化完成
2025-07-26 10:05:24,977 - action - INFO - 临时文件清理完成
2025-07-26 10:05:25,014 - main - INFO - 应用程序正常退出
2025-07-26 10:14:53,201 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:14:53,212 - test_critical_fixes - INFO - 开始关键问题修复验证测试...
2025-07-26 10:14:53,212 - test_critical_fixes - INFO - === 测试运行时错误修复 ===
2025-07-26 10:14:53,282 - test_critical_fixes - ERROR - ❌ 状态栏测试失败: MainWindow.__init__() takes 1 positional argument but 2 were given
2025-07-26 10:14:53,283 - test_critical_fixes - INFO - ✅ OpenCV坐标类型修复成功
2025-07-26 10:14:53,283 - test_critical_fixes - INFO - === 测试UI功能增强 ===
2025-07-26 10:14:53,284 - test_critical_fixes - INFO - ✅ 区域选择器创建成功
2025-07-26 10:14:53,286 - code_generator - INFO - 独立脚本已生成: generated_scripts\test_generated_script.py
2025-07-26 10:14:53,286 - test_critical_fixes - INFO - ✅ 代码生成器测试成功: generated_scripts\test_generated_script.py
2025-07-26 10:14:53,296 - test_critical_fixes - INFO - ✅ 图像导入功能测试成功
2025-07-26 10:14:53,297 - test_critical_fixes - INFO - === 测试性能优化 ===
2025-07-26 10:14:55,602 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:14:55,603 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:14:57,311 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 10:14:57,312 - test_critical_fixes - INFO - ✅ OCR缓存功能正常 (第一次: 1.708s, 第二次: 0.000s)
2025-07-26 10:14:57,313 - test_critical_fixes - INFO - 缓存信息: {'cache_size': 1, 'max_size': 100, 'cache_keys': ['af76ce17474be184_0.8']}
2025-07-26 10:14:57,314 - performance_monitor - INFO - 性能监控历史记录已清除
2025-07-26 10:14:57,421 - test_critical_fixes - INFO - ✅ 性能监控功能正常
2025-07-26 10:14:57,422 - test_critical_fixes - INFO - 性能摘要: {'total_operations': 1, 'time_range': {'start': 1753496097.4218936, 'end': 1753496097.4218936}, 'duration_stats': {'min': 0.10751152038574219, 'max': 0.10751152038574219, 'avg': 0.10751152038574219, 'total': 0.10751152038574219}, 'memory_stats': {'min': 0.00390625, 'max': 0.00390625, 'avg': 0.00390625, 'total': 0.00390625}, 'cpu_stats': {'min': 1376.1, 'max': 1376.1, 'avg': 1376.1}, 'operation_counts': {'test_operation': 1}}
2025-07-26 10:14:58,364 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:14:58,365 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:14:58,423 - test_critical_fixes - INFO - ✅ 图像预处理优化正常 (原始: (2000, 3000, 3), 处理后: (1280, 1920, 3))
2025-07-26 10:14:58,456 - test_critical_fixes - INFO - === 测试内存管理 ===
2025-07-26 10:15:00,458 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:15:00,459 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:15:02,546 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 10:15:04,796 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:06,930 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:08,990 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:11,124 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:13,255 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:15,514 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:17,806 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:20,079 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:22,210 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:15:22,254 - performance_monitor - INFO - 内存优化完成，回收对象: 742, 当前内存: 464.06MB
2025-07-26 10:15:23,281 - test_critical_fixes - INFO - 内存使用: 初始 291.41MB, 最终 464.11MB
2025-07-26 10:15:23,282 - test_critical_fixes - INFO - 内存优化结果: {'collected_objects': 742, 'current_memory_mb': 464.0625}
2025-07-26 10:15:23,283 - test_critical_fixes - INFO - ✅ 内存管理测试完成
2025-07-26 10:15:23,322 - test_critical_fixes - INFO - ============================================================
2025-07-26 10:15:23,322 - test_critical_fixes - INFO - 关键问题修复验证测试总结:
2025-07-26 10:15:23,323 - test_critical_fixes - INFO - 
RUNTIME_FIXES:
2025-07-26 10:15:23,323 - test_critical_fixes - INFO -   status_var_fix: ❌ 失败
2025-07-26 10:15:23,324 - test_critical_fixes - INFO -   opencv_coordinate_fix: ✅ 通过
2025-07-26 10:15:23,324 - test_critical_fixes - INFO - 
UI_ENHANCEMENTS:
2025-07-26 10:15:23,325 - test_critical_fixes - INFO -   region_selector: ✅ 通过
2025-07-26 10:15:23,325 - test_critical_fixes - INFO -   code_generator: ✅ 通过
2025-07-26 10:15:23,326 - test_critical_fixes - INFO -   image_import: ✅ 通过
2025-07-26 10:15:23,326 - test_critical_fixes - INFO - 
PERFORMANCE_OPTIMIZATIONS:
2025-07-26 10:15:23,326 - test_critical_fixes - INFO -   ocr_cache: ✅ 通过
2025-07-26 10:15:23,327 - test_critical_fixes - INFO -   performance_monitor: ✅ 通过
2025-07-26 10:15:23,327 - test_critical_fixes - INFO -   image_preprocessing: ✅ 通过
2025-07-26 10:15:23,327 - test_critical_fixes - INFO - 
MEMORY_MANAGEMENT:
2025-07-26 10:15:23,328 - test_critical_fixes - INFO -   memory_management: ✅ 通过
2025-07-26 10:15:23,328 - test_critical_fixes - INFO - 
总体结果: 8/9 测试通过
2025-07-26 10:15:23,328 - test_critical_fixes - WARNING - ⚠️ 1 个测试失败
2025-07-26 10:15:23,330 - performance_monitor - INFO - 性能指标已导出到: temp/performance_test_results.json
2025-07-26 10:15:23,330 - test_critical_fixes - INFO - 性能测试数据已导出到 temp/performance_test_results.json
2025-07-26 10:16:19,345 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:16:19,353 - test_critical_fixes - INFO - 开始关键问题修复验证测试...
2025-07-26 10:16:19,354 - test_critical_fixes - INFO - === 测试运行时错误修复 ===
2025-07-26 10:16:19,410 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:16:21,802 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:21,803 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:21,803 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:16:21,804 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:16:21,804 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:16:21,804 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:16:21,848 - gui - INFO - 主窗口初始化完成
2025-07-26 10:16:21,849 - test_critical_fixes - INFO - ✅ status_var属性修复成功
2025-07-26 10:16:21,884 - test_critical_fixes - INFO - ✅ OpenCV坐标类型修复成功
2025-07-26 10:16:21,885 - test_critical_fixes - INFO - === 测试UI功能增强 ===
2025-07-26 10:16:21,886 - test_critical_fixes - INFO - ✅ 区域选择器创建成功
2025-07-26 10:16:21,887 - code_generator - INFO - 独立脚本已生成: generated_scripts\test_generated_script.py
2025-07-26 10:16:21,888 - test_critical_fixes - INFO - ✅ 代码生成器测试成功: generated_scripts\test_generated_script.py
2025-07-26 10:16:21,891 - test_critical_fixes - INFO - ✅ 图像导入功能测试成功
2025-07-26 10:16:21,892 - test_critical_fixes - INFO - === 测试性能优化 ===
2025-07-26 10:16:22,883 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:22,883 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:24,488 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 10:16:24,489 - test_critical_fixes - INFO - ✅ OCR缓存功能正常 (第一次: 1.604s, 第二次: 0.001s)
2025-07-26 10:16:24,489 - test_critical_fixes - INFO - 缓存信息: {'cache_size': 1, 'max_size': 100, 'cache_keys': ['af76ce17474be184_0.8']}
2025-07-26 10:16:24,490 - performance_monitor - INFO - 性能监控历史记录已清除
2025-07-26 10:16:24,604 - test_critical_fixes - INFO - ✅ 性能监控功能正常
2025-07-26 10:16:24,604 - test_critical_fixes - INFO - 性能摘要: {'total_operations': 1, 'time_range': {'start': 1753496184.604868, 'end': 1753496184.604868}, 'duration_stats': {'min': 0.11462187767028809, 'max': 0.11462187767028809, 'avg': 0.11462187767028809, 'total': 0.11462187767028809}, 'memory_stats': {'min': 0.00390625, 'max': 0.00390625, 'avg': 0.00390625, 'total': 0.00390625}, 'cpu_stats': {'min': 1400.0, 'max': 1400.0, 'avg': 1400.0}, 'operation_counts': {'test_operation': 1}}
2025-07-26 10:16:25,575 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:25,575 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:25,633 - test_critical_fixes - INFO - ✅ 图像预处理优化正常 (原始: (2000, 3000, 3), 处理后: (1280, 1920, 3))
2025-07-26 10:16:25,666 - test_critical_fixes - INFO - === 测试内存管理 ===
2025-07-26 10:16:27,695 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:16:27,697 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:16:29,903 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:32,156 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:34,255 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:36,659 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:38,765 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:40,919 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:43,268 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:45,636 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:47,878 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:50,290 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 10:16:50,335 - performance_monitor - INFO - 内存优化完成，回收对象: 922, 当前内存: 561.18MB
2025-07-26 10:16:51,357 - test_critical_fixes - INFO - 内存使用: 初始 379.78MB, 最终 561.18MB
2025-07-26 10:16:51,357 - test_critical_fixes - INFO - 内存优化结果: {'collected_objects': 922, 'current_memory_mb': 561.17578125}
2025-07-26 10:16:51,359 - test_critical_fixes - INFO - ✅ 内存管理测试完成
2025-07-26 10:16:51,399 - test_critical_fixes - INFO - ============================================================
2025-07-26 10:16:51,400 - test_critical_fixes - INFO - 关键问题修复验证测试总结:
2025-07-26 10:16:51,401 - test_critical_fixes - INFO - 
RUNTIME_FIXES:
2025-07-26 10:16:51,401 - test_critical_fixes - INFO -   status_var_fix: ✅ 通过
2025-07-26 10:16:51,402 - test_critical_fixes - INFO -   opencv_coordinate_fix: ✅ 通过
2025-07-26 10:16:51,402 - test_critical_fixes - INFO - 
UI_ENHANCEMENTS:
2025-07-26 10:16:51,403 - test_critical_fixes - INFO -   region_selector: ✅ 通过
2025-07-26 10:16:51,403 - test_critical_fixes - INFO -   code_generator: ✅ 通过
2025-07-26 10:16:51,403 - test_critical_fixes - INFO -   image_import: ✅ 通过
2025-07-26 10:16:51,404 - test_critical_fixes - INFO - 
PERFORMANCE_OPTIMIZATIONS:
2025-07-26 10:16:51,404 - test_critical_fixes - INFO -   ocr_cache: ✅ 通过
2025-07-26 10:16:51,404 - test_critical_fixes - INFO -   performance_monitor: ✅ 通过
2025-07-26 10:16:51,405 - test_critical_fixes - INFO -   image_preprocessing: ✅ 通过
2025-07-26 10:16:51,405 - test_critical_fixes - INFO - 
MEMORY_MANAGEMENT:
2025-07-26 10:16:51,406 - test_critical_fixes - INFO -   memory_management: ✅ 通过
2025-07-26 10:16:51,406 - test_critical_fixes - INFO - 
总体结果: 9/9 测试通过
2025-07-26 10:16:51,407 - test_critical_fixes - INFO - 🎉 所有关键问题修复验证测试通过！
2025-07-26 10:16:51,409 - performance_monitor - INFO - 性能指标已导出到: temp/performance_test_results.json
2025-07-26 10:16:51,409 - test_critical_fixes - INFO - 性能测试数据已导出到 temp/performance_test_results.json
2025-07-26 10:17:09,378 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:17:09,388 - main - INFO - ==================================================
2025-07-26 10:17:09,388 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:17:09,389 - main - INFO - ==================================================
2025-07-26 10:17:10,812 - main - INFO - 所有依赖包检查通过
2025-07-26 10:17:10,813 - main - INFO - 目录结构检查完成
2025-07-26 10:17:10,814 - main - INFO - 启动GUI界面...
2025-07-26 10:17:10,860 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:17:11,794 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:17:11,795 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:17:11,796 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:17:11,796 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:17:11,797 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:17:11,797 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:17:11,833 - gui - INFO - 主窗口初始化完成
2025-07-26 10:17:19,952 - region_selector - INFO - 开始区域选择
2025-07-26 10:17:20,582 - region_selector - INFO - 区域选择窗口已创建
2025-07-26 10:17:22,448 - region_selector - INFO - 选择区域: (574, 328, 30, 26)
2025-07-26 10:17:24,807 - region_selector - INFO - 确认选择区域: (574, 328, 30, 26)
2025-07-26 10:17:24,809 - gui - ERROR - 区域截图回调失败: 'ScreenCapture' object has no attribute 'capture_region'
2025-07-26 10:17:27,863 - region_selector - WARNING - 选择区域太小，请重新选择
2025-07-26 10:17:29,442 - region_selector - INFO - 确认选择区域: (986, 514, 0, 0)
2025-07-26 10:17:29,443 - gui - ERROR - 区域截图回调失败: 'ScreenCapture' object has no attribute 'capture_region'
2025-07-26 10:17:30,714 - region_selector - WARNING - 选择区域太小，请重新选择
2025-07-26 10:17:32,038 - region_selector - INFO - 用户取消区域选择
2025-07-26 10:17:32,078 - region_selector - INFO - 区域选择器已清理
2025-07-26 10:17:36,021 - region_selector - INFO - 区域选择器已清理
2025-07-26 10:17:37,973 - region_selector - INFO - 区域选择器已清理
2025-07-26 10:18:02,843 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:18:02,854 - main - INFO - ==================================================
2025-07-26 10:18:02,854 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:18:02,855 - main - INFO - ==================================================
2025-07-26 10:18:04,218 - main - INFO - 所有依赖包检查通过
2025-07-26 10:18:04,219 - main - INFO - 目录结构检查完成
2025-07-26 10:18:04,220 - main - INFO - 启动GUI界面...
2025-07-26 10:18:04,273 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:18:05,208 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:18:05,209 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:18:05,209 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:18:05,210 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:18:05,211 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:18:05,211 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:18:05,249 - gui - INFO - 主窗口初始化完成
2025-07-26 10:46:39,357 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:46:39,386 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:47:21,846 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:47:21,859 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:47:21,863 - ux_test - INFO - 开始用户体验测试...
2025-07-26 10:47:21,868 - ux_test - INFO - 测试图像创建完成
2025-07-26 10:47:21,917 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:24,379 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:47:24,380 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:47:24,380 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:24,381 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:24,381 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:24,382 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:47:24,385 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 10:47:24,386 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 10:47:24,394 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 10:47:24,395 - ux_test - ERROR - 创建主窗口失败: 'MainWindow' object has no attribute 'batch_click_search_results'
2025-07-26 10:47:46,647 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:47:46,661 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:47:46,665 - ux_test - INFO - 开始用户体验测试...
2025-07-26 10:47:46,670 - ux_test - INFO - 测试图像创建完成
2025-07-26 10:47:46,724 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:49,140 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:47:49,141 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:47:49,141 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:49,142 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:47:49,142 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:47:49,142 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:47:49,144 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 10:47:49,145 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 10:47:49,154 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 10:47:49,160 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,161 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,162 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,163 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,166 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,168 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,169 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,171 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,173 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,174 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,175 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,175 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,178 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:47:49,178 - gui - INFO - 增强工具栏创建完成
2025-07-26 10:47:49,196 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 10:47:49,200 - gui - INFO - 主窗口初始化完成
2025-07-26 10:47:49,201 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 10:47:49,202 - ux_test - INFO - 主窗口创建完成
2025-07-26 10:47:49,203 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 10:47:49,381 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 10:47:49,382 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 10:47:49,383 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 10:47:49,383 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 10:47:49,384 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 10:47:49,384 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 10:47:49,441 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 10:47:49,441 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 10:47:49,442 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 10:47:49,444 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 10:47:49,445 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 10:47:49,445 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 10:47:49,446 - ux_test - INFO - 帮助系统测试完成
2025-07-26 10:47:49,446 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 10:47:49,446 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 10:47:49,460 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 10:47:49,462 - ux_test - INFO - 响应性能测试完成
2025-07-26 10:47:49,462 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 10:47:49,463 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 10:47:49,463 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 10:47:49,464 - ux_test - INFO - 错误处理测试完成
2025-07-26 10:47:49,464 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 10:47:49,464 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 10:47:49,465 - ux_test - INFO - 可访问性测试完成
2025-07-26 10:47:49,465 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 10:47:49,466 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 10:47:49,538 - ux_test - INFO - 测试资源清理完成
2025-07-26 10:49:53,479 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 10:49:53,492 - help_system - INFO - 帮助系统初始化完成
2025-07-26 10:49:53,495 - main - INFO - ==================================================
2025-07-26 10:49:53,496 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 10:49:53,496 - main - INFO - ==================================================
2025-07-26 10:49:54,861 - main - INFO - 所有依赖包检查通过
2025-07-26 10:49:54,862 - main - INFO - 目录结构检查完成
2025-07-26 10:49:54,862 - main - INFO - 启动GUI界面...
2025-07-26 10:49:54,909 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:49:56,298 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 10:49:56,299 - ocr - INFO - OCREngine初始化完成
2025-07-26 10:49:56,299 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:49:56,300 - screen - INFO - ScreenCapture初始化完成
2025-07-26 10:49:56,300 - match - INFO - TemplateMatch初始化完成
2025-07-26 10:49:56,300 - action - INFO - ActionExecutor初始化完成
2025-07-26 10:49:56,302 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 10:49:56,303 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 10:49:56,317 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 10:49:56,323 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,324 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,325 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,327 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,328 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,329 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,330 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,333 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,334 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,338 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,340 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,343 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,347 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 10:49:56,348 - gui - INFO - 增强工具栏创建完成
2025-07-26 10:49:56,378 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 10:49:56,382 - gui - INFO - 主窗口初始化完成
2025-07-26 11:03:06,101 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:03:06,118 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:03:06,122 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:03:06,128 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:03:06,181 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:03:08,822 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:03:08,823 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:03:08,824 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:03:08,825 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:03:08,825 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:03:08,826 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:03:08,827 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:03:08,828 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:03:08,840 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:03:08,848 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,850 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,851 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,852 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,855 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,857 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,859 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,861 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,862 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,864 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,865 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,867 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,868 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:03:08,869 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:03:08,893 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:03:08,897 - gui - INFO - 主窗口初始化完成
2025-07-26 11:03:08,898 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:03:08,899 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:03:08,899 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:03:09,022 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:03:09,022 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:03:09,023 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:03:09,023 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:03:09,024 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:03:09,024 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:03:09,066 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:03:09,067 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:03:09,067 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:03:09,069 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:03:09,070 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:03:09,070 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:03:09,071 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:03:09,071 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:03:09,071 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:03:09,082 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:03:09,082 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:03:09,083 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:03:09,083 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:03:09,084 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:03:09,084 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:03:09,085 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:03:09,085 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:03:09,086 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:03:09,086 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:03:09,087 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:03:09,133 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:21:29,107 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:21:29,157 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:21:29,161 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:21:29,167 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:21:29,668 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:21:40,701 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:21:40,702 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:21:40,702 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:21:40,703 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:21:40,704 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:21:40,705 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:21:40,708 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:21:40,709 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:21:40,758 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:21:40,769 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,772 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,774 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,777 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,780 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,783 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,784 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,790 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,796 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,808 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,812 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,816 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,821 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:21:40,822 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:21:40,917 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:21:40,923 - gui - INFO - 主窗口初始化完成
2025-07-26 11:21:40,926 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:21:40,926 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:21:40,927 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:21:41,222 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:21:41,223 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:21:41,224 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:21:41,225 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:21:41,225 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:21:41,226 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:21:41,299 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:21:41,300 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:21:41,301 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:21:41,305 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:21:41,306 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:21:41,306 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:21:41,308 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:21:41,309 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:21:41,310 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:21:41,330 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:21:41,332 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:21:41,332 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:21:41,333 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:21:41,335 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:21:41,336 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:21:41,337 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:21:41,338 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:21:41,339 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:21:41,340 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:21:41,343 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:21:41,439 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:22:33,778 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:22:33,801 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:22:33,804 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:22:33,810 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:22:33,867 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:22:40,589 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:22:40,589 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:22:40,590 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:22:40,591 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:22:40,592 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:22:40,592 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:22:40,594 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:22:40,595 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:22:40,609 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:22:40,617 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,619 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,625 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,627 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,631 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,633 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,635 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,637 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,639 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,643 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,644 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,647 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,648 - enhanced_toolbar - ERROR - 创建按钮失败: unknown option "-relief"
2025-07-26 11:22:40,649 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:22:40,680 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:22:40,686 - gui - INFO - 主窗口初始化完成
2025-07-26 11:22:40,688 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:22:40,689 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:22:40,690 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:22:40,983 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:22:40,984 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:22:40,984 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:22:40,985 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:22:40,986 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:22:40,986 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:22:41,049 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:22:41,050 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:22:41,051 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:22:41,054 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:22:41,055 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:22:41,057 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:22:41,058 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:22:41,058 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:22:41,059 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:22:41,158 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:22:41,159 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:22:41,160 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:22:41,161 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:22:41,162 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:22:41,163 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:22:41,163 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:22:41,164 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:22:41,164 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:22:41,165 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:22:41,166 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:22:41,324 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:23:11,905 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:23:11,924 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:23:11,927 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:23:11,932 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:23:11,979 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:23:14,650 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:23:14,651 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:23:14,651 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:23:14,651 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:23:14,652 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:23:14,652 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:23:14,654 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:23:14,654 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:23:14,664 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:23:14,676 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:23:14,695 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:23:14,699 - gui - INFO - 主窗口初始化完成
2025-07-26 11:23:14,700 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:23:14,700 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:23:14,701 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:23:14,960 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:23:14,961 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:23:14,961 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:23:14,962 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:23:14,962 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:23:14,963 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:23:15,003 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:23:15,004 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:23:15,004 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:23:15,006 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:23:15,007 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:23:15,007 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:23:15,008 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:23:15,009 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:23:15,010 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:23:15,022 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:23:15,023 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:23:15,024 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:23:15,025 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:23:15,026 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:23:15,027 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:23:15,028 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:23:15,029 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:23:15,029 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:23:15,029 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:23:15,030 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:23:15,113 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:23:54,037 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:23:54,053 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:23:54,057 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:23:54,062 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:23:54,110 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:23:56,807 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:23:56,808 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:23:56,809 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:23:56,809 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:23:56,810 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:23:56,810 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:23:56,812 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:23:56,813 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:23:56,824 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:23:56,839 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:23:56,896 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:23:56,904 - gui - INFO - 主窗口初始化完成
2025-07-26 11:23:56,906 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:23:56,907 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:23:56,908 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:23:57,070 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:23:57,070 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:23:57,071 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:23:57,071 - ux_test - INFO - 调试：toolbar.buttons内容: ['全屏截图', '区域截图', '交互选择', '导入图像', 'OCR识别', '文本搜索', '模板匹配', '点击测试', '批量操作', '生成代码', '文件管理', '配置', '清理']
2025-07-26 11:23:57,072 - ux_test - INFO - 调试：button_groups内容: {'图像获取': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button3>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button4>], 'config': ToolbarGroup(name='图像获取', buttons=[ToolbarButton(text='全屏截图', command=<bound method MainWindow.capture_fullscreen of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='截取整个屏幕\n适用于全屏应用分析', icon=None, shortcut='Ctrl+1', enabled=True, style='default'), ToolbarButton(text='区域截图', command=<bound method MainWindow.capture_region of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='截取指定矩形区域\n适用于局部区域分析', icon=None, shortcut='Ctrl+2', enabled=True, style='default'), ToolbarButton(text='交互选择', command=<bound method MainWindow.interactive_region_capture of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='交互式拖拽选择区域\n提供实时预览和尺寸显示', icon=None, shortcut='Ctrl+3', enabled=True, style='default'), ToolbarButton(text='导入图像', command=<bound method MainWindow.import_image of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='从本地文件导入图像\n支持PNG、JPG、BMP等格式', icon=None, shortcut='Ctrl+O', enabled=True, style='default')], collapsible=False, expanded=True)}, '识别分析': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe2>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe2.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button3>], 'config': ToolbarGroup(name='识别分析', buttons=[ToolbarButton(text='OCR识别', command=<bound method MainWindow.run_ocr of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='识别图像中的文本内容\n支持中英文混合识别', icon=None, shortcut='Ctrl+R', enabled=True, style='primary'), ToolbarButton(text='文本搜索', command=<bound method MainWindow.show_text_search of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='在识别结果中搜索特定文本\n支持多种匹配模式', icon=None, shortcut='Ctrl+F', enabled=True, style='default'), ToolbarButton(text='模板匹配', command=<bound method MainWindow.run_template_match of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='使用模板图像进行匹配\n适用于图标和界面元素识别', icon=None, shortcut='Ctrl+M', enabled=True, style='default')], collapsible=False, expanded=True)}, '自动化操作': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe3>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe3.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe3.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe3.!frame.!button2>], 'config': ToolbarGroup(name='自动化操作', buttons=[ToolbarButton(text='点击测试', command=<bound method MainWindow.test_click of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='测试自动点击功能\n验证坐标定位准确性', icon=None, shortcut='Ctrl+T', enabled=True, style='default'), ToolbarButton(text='批量操作', command=<bound method MainWindow.batch_click_results of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='执行批量自动化操作\n基于识别结果进行批量点击', icon=None, shortcut='Ctrl+B', enabled=True, style='default')], collapsible=False, expanded=True)}, '工具': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe4>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe4.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button3>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button4>], 'config': ToolbarGroup(name='工具', buttons=[ToolbarButton(text='生成代码', command=<bound method MainWindow.generate_code of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='根据当前配置生成Python代码\n支持独立脚本和项目结构', icon=None, shortcut='Ctrl+G', enabled=True, style='default'), ToolbarButton(text='文件管理', command=<bound method MainWindow.show_file_manager of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='管理截图和结果文件\n提供文件浏览和清理功能', icon=None, shortcut='Ctrl+E', enabled=True, style='default'), ToolbarButton(text='配置', command=<bound method MainWindow.open_config_dialog of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='打开应用程序配置对话框\n调整OCR和匹配参数', icon=None, shortcut='Ctrl+,', enabled=True, style='default'), ToolbarButton(text='清理', command=<bound method MainWindow.clear_cache of <gui.main_window.MainWindow object at 0x0000021AFFE6A0E0>>, tooltip='清理临时文件和缓存\n释放存储空间', icon=None, shortcut=None, enabled=True, style='secondary')], collapsible=False, expanded=True)}}
2025-07-26 11:23:57,073 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:23:57,074 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:23:57,074 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:23:57,141 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:23:57,142 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:23:57,142 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:23:57,144 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:23:57,145 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:23:57,145 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:23:57,146 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:23:57,146 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:23:57,147 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:23:57,164 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:23:57,165 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:23:57,165 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:23:57,166 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:23:57,166 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:23:57,167 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:23:57,167 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:23:57,167 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:23:57,168 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:23:57,168 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:23:57,168 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:23:57,223 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:24:42,117 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:24:42,133 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:24:42,138 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:24:42,143 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:24:42,189 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:24:44,746 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:24:44,749 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:24:44,751 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:24:44,752 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:24:44,754 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:24:44,754 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:24:44,756 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:24:44,757 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:24:44,775 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:24:44,803 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:24:44,824 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:24:44,828 - gui - INFO - 主窗口初始化完成
2025-07-26 11:24:44,829 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:24:44,829 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:24:44,829 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:24:44,960 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:24:44,970 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:24:44,971 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:24:44,973 - ux_test - INFO - 调试：toolbar.buttons内容: ['全屏截图', '区域截图', '交互选择', '导入图像', 'OCR识别', '文本搜索', '模板匹配', '点击测试', '批量操作', '生成代码', '文件管理', '配置', '清理']
2025-07-26 11:24:44,974 - ux_test - INFO - 调试：button_groups内容: {'图像获取': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button3>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button4>], 'config': ToolbarGroup(name='图像获取', buttons=[ToolbarButton(text='全屏截图', command=<bound method MainWindow.capture_fullscreen of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='截取整个屏幕\n适用于全屏应用分析', icon=None, shortcut='Ctrl+1', enabled=True, style='default'), ToolbarButton(text='区域截图', command=<bound method MainWindow.capture_region of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='截取指定矩形区域\n适用于局部区域分析', icon=None, shortcut='Ctrl+2', enabled=True, style='default'), ToolbarButton(text='交互选择', command=<bound method MainWindow.interactive_region_capture of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='交互式拖拽选择区域\n提供实时预览和尺寸显示', icon=None, shortcut='Ctrl+3', enabled=True, style='default'), ToolbarButton(text='导入图像', command=<bound method MainWindow.import_image of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='从本地文件导入图像\n支持PNG、JPG、BMP等格式', icon=None, shortcut='Ctrl+O', enabled=True, style='default')], collapsible=False, expanded=True)}, '识别分析': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe2>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe2.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button3>], 'config': ToolbarGroup(name='识别分析', buttons=[ToolbarButton(text='OCR识别', command=<bound method MainWindow.run_ocr of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='识别图像中的文本内容\n支持中英文混合识别', icon=None, shortcut='Ctrl+R', enabled=True, style='primary'), ToolbarButton(text='文本搜索', command=<bound method MainWindow.show_text_search of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='在识别结果中搜索特定文本\n支持多种匹配模式', icon=None, shortcut='Ctrl+F', enabled=True, style='default'), ToolbarButton(text='模板匹配', command=<bound method MainWindow.run_template_match of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='使用模板图像进行匹配\n适用于图标和界面元素识别', icon=None, shortcut='Ctrl+M', enabled=True, style='default')], collapsible=False, expanded=True)}, '自动化操作': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe3>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe3.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe3.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe3.!frame.!button2>], 'config': ToolbarGroup(name='自动化操作', buttons=[ToolbarButton(text='点击测试', command=<bound method MainWindow.test_click of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='测试自动点击功能\n验证坐标定位准确性', icon=None, shortcut='Ctrl+T', enabled=True, style='default'), ToolbarButton(text='批量操作', command=<bound method MainWindow.batch_click_results of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='执行批量自动化操作\n基于识别结果进行批量点击', icon=None, shortcut='Ctrl+B', enabled=True, style='default')], collapsible=False, expanded=True)}, '工具': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe4>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe4.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button3>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button4>], 'config': ToolbarGroup(name='工具', buttons=[ToolbarButton(text='生成代码', command=<bound method MainWindow.generate_code of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='根据当前配置生成Python代码\n支持独立脚本和项目结构', icon=None, shortcut='Ctrl+G', enabled=True, style='default'), ToolbarButton(text='文件管理', command=<bound method MainWindow.show_file_manager of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='管理截图和结果文件\n提供文件浏览和清理功能', icon=None, shortcut='Ctrl+E', enabled=True, style='default'), ToolbarButton(text='配置', command=<bound method MainWindow.open_config_dialog of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='打开应用程序配置对话框\n调整OCR和匹配参数', icon=None, shortcut='Ctrl+,', enabled=True, style='default'), ToolbarButton(text='清理', command=<bound method MainWindow.clear_cache of <gui.main_window.MainWindow object at 0x000001ADEE0EAB90>>, tooltip='清理临时文件和缓存\n释放存储空间', icon=None, shortcut=None, enabled=True, style='secondary')], collapsible=False, expanded=True)}}
2025-07-26 11:24:44,980 - ux_test - INFO - 调试：开始检查13个按钮的状态
2025-07-26 11:24:44,981 - ux_test - INFO - 调试：按钮 全屏截图 状态: normal
2025-07-26 11:24:44,982 - ux_test - INFO - 调试：按钮 区域截图 状态: normal
2025-07-26 11:24:44,982 - ux_test - INFO - 调试：按钮 交互选择 状态: normal
2025-07-26 11:24:44,983 - ux_test - INFO - 调试：按钮 导入图像 状态: normal
2025-07-26 11:24:44,987 - ux_test - INFO - 调试：按钮 OCR识别 状态: normal
2025-07-26 11:24:44,989 - ux_test - INFO - 调试：按钮 文本搜索 状态: normal
2025-07-26 11:24:44,990 - ux_test - INFO - 调试：按钮 模板匹配 状态: normal
2025-07-26 11:24:44,991 - ux_test - INFO - 调试：按钮 点击测试 状态: normal
2025-07-26 11:24:44,992 - ux_test - INFO - 调试：按钮 批量操作 状态: normal
2025-07-26 11:24:44,996 - ux_test - INFO - 调试：按钮 生成代码 状态: normal
2025-07-26 11:24:44,996 - ux_test - INFO - 调试：按钮 文件管理 状态: normal
2025-07-26 11:24:44,998 - ux_test - INFO - 调试：按钮 配置 状态: normal
2025-07-26 11:24:45,000 - ux_test - INFO - 调试：按钮 清理 状态: normal
2025-07-26 11:24:45,001 - ux_test - INFO - 调试：最终统计 - 总按钮数: 13, 启用按钮数: 13
2025-07-26 11:24:45,003 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:24:45,006 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:24:45,008 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:24:45,063 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:24:45,064 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:24:45,064 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:24:45,066 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:24:45,066 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:24:45,067 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:24:45,067 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:24:45,067 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:24:45,068 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:24:45,077 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:24:45,077 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:24:45,078 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:24:45,079 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:24:45,080 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:24:45,080 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:24:45,081 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:24:45,081 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:24:45,081 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:24:45,082 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:24:45,083 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:24:45,125 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:26:03,254 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:26:03,271 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:26:03,285 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:26:03,293 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:26:03,361 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:26:05,942 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:26:05,944 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:26:05,945 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:26:05,946 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:26:05,947 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:26:05,947 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:26:05,949 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:26:05,952 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:26:05,962 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:26:05,975 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:26:05,992 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:26:05,999 - gui - INFO - 快捷键绑定完成
2025-07-26 11:26:06,026 - gui - INFO - 主窗口初始化完成
2025-07-26 11:26:06,028 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:26:06,030 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:26:06,031 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:26:06,160 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:26:06,161 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:26:06,163 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:26:06,163 - ux_test - INFO - 调试：toolbar.buttons内容: ['全屏截图', '区域截图', '交互选择', '导入图像', 'OCR识别', '文本搜索', '模板匹配', '点击测试', '批量操作', '生成代码', '文件管理', '配置', '清理']
2025-07-26 11:26:06,164 - ux_test - INFO - 调试：button_groups内容: {'图像获取': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button3>, <tkinter.ttk.Button object .!frame.!labelframe.!frame.!button4>], 'config': ToolbarGroup(name='图像获取', buttons=[ToolbarButton(text='全屏截图', command=<bound method MainWindow.capture_fullscreen of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='截取整个屏幕\n适用于全屏应用分析', icon=None, shortcut='Ctrl+1', enabled=True, style='default'), ToolbarButton(text='区域截图', command=<bound method MainWindow.capture_region of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='截取指定矩形区域\n适用于局部区域分析', icon=None, shortcut='Ctrl+2', enabled=True, style='default'), ToolbarButton(text='交互选择', command=<bound method MainWindow.interactive_region_capture of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='交互式拖拽选择区域\n提供实时预览和尺寸显示', icon=None, shortcut='Ctrl+3', enabled=True, style='default'), ToolbarButton(text='导入图像', command=<bound method MainWindow.import_image of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='从本地文件导入图像\n支持PNG、JPG、BMP等格式', icon=None, shortcut='Ctrl+O', enabled=True, style='default')], collapsible=False, expanded=True)}, '识别分析': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe2>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe2.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe2.!frame.!button3>], 'config': ToolbarGroup(name='识别分析', buttons=[ToolbarButton(text='OCR识别', command=<bound method MainWindow.run_ocr of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='识别图像中的文本内容\n支持中英文混合识别', icon=None, shortcut='Ctrl+R', enabled=True, style='primary'), ToolbarButton(text='文本搜索', command=<bound method MainWindow.show_text_search of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='在识别结果中搜索特定文本\n支持多种匹配模式', icon=None, shortcut='Ctrl+F', enabled=True, style='default'), ToolbarButton(text='模板匹配', command=<bound method MainWindow.run_template_match of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='使用模板图像进行匹配\n适用于图标和界面元素识别', icon=None, shortcut='Ctrl+M', enabled=True, style='default')], collapsible=False, expanded=True)}, '自动化操作': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe3>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe3.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe3.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe3.!frame.!button2>], 'config': ToolbarGroup(name='自动化操作', buttons=[ToolbarButton(text='点击测试', command=<bound method MainWindow.test_click of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='测试自动点击功能\n验证坐标定位准确性', icon=None, shortcut='Ctrl+T', enabled=True, style='default'), ToolbarButton(text='批量操作', command=<bound method MainWindow.batch_click_results of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='执行批量自动化操作\n基于识别结果进行批量点击', icon=None, shortcut='Ctrl+B', enabled=True, style='default')], collapsible=False, expanded=True)}, '工具': {'frame': <tkinter.ttk.Labelframe object .!frame.!labelframe4>, 'container': <tkinter.ttk.Frame object .!frame.!labelframe4.!frame>, 'buttons': [<tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button2>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button3>, <tkinter.ttk.Button object .!frame.!labelframe4.!frame.!button4>], 'config': ToolbarGroup(name='工具', buttons=[ToolbarButton(text='生成代码', command=<bound method MainWindow.generate_code of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='根据当前配置生成Python代码\n支持独立脚本和项目结构', icon=None, shortcut='Ctrl+G', enabled=True, style='default'), ToolbarButton(text='文件管理', command=<bound method MainWindow.show_file_manager of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='管理截图和结果文件\n提供文件浏览和清理功能', icon=None, shortcut='Ctrl+E', enabled=True, style='default'), ToolbarButton(text='配置', command=<bound method MainWindow.open_config_dialog of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='打开应用程序配置对话框\n调整OCR和匹配参数', icon=None, shortcut='Ctrl+,', enabled=True, style='default'), ToolbarButton(text='清理', command=<bound method MainWindow.clear_cache of <gui.main_window.MainWindow object at 0x0000029AF9C683A0>>, tooltip='清理临时文件和缓存\n释放存储空间', icon=None, shortcut=None, enabled=True, style='secondary')], collapsible=False, expanded=True)}}
2025-07-26 11:26:06,166 - ux_test - INFO - 调试：开始检查13个按钮的状态
2025-07-26 11:26:06,169 - ux_test - INFO - 调试：按钮 全屏截图 状态: normal
2025-07-26 11:26:06,171 - ux_test - INFO - 调试：按钮 区域截图 状态: normal
2025-07-26 11:26:06,171 - ux_test - INFO - 调试：按钮 交互选择 状态: normal
2025-07-26 11:26:06,172 - ux_test - INFO - 调试：按钮 导入图像 状态: normal
2025-07-26 11:26:06,173 - ux_test - INFO - 调试：按钮 OCR识别 状态: normal
2025-07-26 11:26:06,176 - ux_test - INFO - 调试：按钮 文本搜索 状态: normal
2025-07-26 11:26:06,178 - ux_test - INFO - 调试：按钮 模板匹配 状态: normal
2025-07-26 11:26:06,179 - ux_test - INFO - 调试：按钮 点击测试 状态: normal
2025-07-26 11:26:06,180 - ux_test - INFO - 调试：按钮 批量操作 状态: normal
2025-07-26 11:26:06,181 - ux_test - INFO - 调试：按钮 生成代码 状态: normal
2025-07-26 11:26:06,182 - ux_test - INFO - 调试：按钮 文件管理 状态: normal
2025-07-26 11:26:06,183 - ux_test - INFO - 调试：按钮 配置 状态: normal
2025-07-26 11:26:06,184 - ux_test - INFO - 调试：按钮 清理 状态: normal
2025-07-26 11:26:06,185 - ux_test - INFO - 调试：最终统计 - 总按钮数: 13, 启用按钮数: 13
2025-07-26 11:26:06,186 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:26:06,187 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:26:06,188 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:26:06,241 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:26:06,241 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:26:06,242 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:26:06,244 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:26:06,244 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:26:06,244 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:26:06,245 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:26:06,245 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:26:06,245 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:26:06,254 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:26:06,255 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:26:06,255 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:26:06,256 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:26:06,256 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:26:06,257 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:26:06,257 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:26:06,257 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:26:06,258 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:26:06,258 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:26:06,259 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:26:06,305 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:26:28,586 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:26:28,601 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:26:28,662 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:26:31,276 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:26:31,278 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:26:31,279 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:26:31,280 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:26:31,281 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:26:31,287 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:26:31,290 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:26:31,291 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:26:31,306 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:26:31,331 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:26:31,357 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:26:31,366 - gui - INFO - 快捷键绑定完成
2025-07-26 11:26:31,382 - gui - INFO - 主窗口初始化完成
2025-07-26 11:27:09,835 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:27:09,852 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:27:09,912 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:27:12,594 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:27:12,598 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:27:12,603 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:27:12,612 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:27:12,614 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:27:12,618 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:27:12,621 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:27:12,623 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:27:12,634 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:27:12,656 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:27:12,675 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:27:12,680 - gui - INFO - 快捷键绑定完成
2025-07-26 11:27:12,685 - gui - INFO - 主窗口初始化完成
2025-07-26 11:27:41,537 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:27:41,554 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:27:41,558 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:27:41,564 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:27:41,612 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:27:44,407 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:27:44,407 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:27:44,408 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:27:44,408 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:27:44,409 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:27:44,409 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:27:44,411 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:27:44,411 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:27:44,434 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:27:44,452 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:27:44,473 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:27:44,477 - gui - INFO - 快捷键绑定完成
2025-07-26 11:27:44,480 - gui - INFO - 主窗口初始化完成
2025-07-26 11:27:44,482 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:27:44,482 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:27:44,483 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:27:44,656 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:27:44,657 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:27:44,658 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:27:44,658 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:27:44,658 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:27:44,659 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:27:44,712 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:27:44,713 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:27:44,713 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:27:44,715 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:27:44,716 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:27:44,716 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:27:44,716 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:27:44,717 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:27:44,717 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:27:44,734 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:27:44,735 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:27:44,736 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:27:44,736 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:27:44,737 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:27:44,737 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:27:44,737 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:27:44,738 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:27:44,738 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:27:44,739 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:27:44,740 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:27:44,798 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:28:23,141 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:28:23,165 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:28:23,169 - ux_test - INFO - 开始用户体验测试...
2025-07-26 11:28:23,175 - ux_test - INFO - 测试图像创建完成
2025-07-26 11:28:23,232 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:28:26,725 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:28:26,726 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:28:26,727 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:28:26,727 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:28:26,728 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:28:26,728 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:28:26,730 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:28:26,731 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:28:26,748 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:28:26,766 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:28:26,792 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:28:26,798 - gui - INFO - 快捷键绑定完成
2025-07-26 11:28:26,803 - gui - INFO - 主窗口初始化完成
2025-07-26 11:28:26,804 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:28:26,804 - ux_test - INFO - 主窗口创建完成
2025-07-26 11:28:26,805 - ux_test - INFO - 执行测试: 界面启动测试
2025-07-26 11:28:26,996 - ux_test - INFO - 界面启动测试完成，耗时: 0.000秒
2025-07-26 11:28:26,996 - ux_test - INFO - 测试完成: 界面启动测试 - 通过
2025-07-26 11:28:26,997 - ux_test - INFO - 执行测试: 工具栏功能测试
2025-07-26 11:28:26,997 - ux_test - INFO - 工具栏功能测试完成
2025-07-26 11:28:26,998 - ux_test - INFO - 测试完成: 工具栏功能测试 - 通过
2025-07-26 11:28:26,998 - ux_test - INFO - 执行测试: 图像预览交互测试
2025-07-26 11:28:27,072 - ux_test - INFO - 图像预览交互测试完成
2025-07-26 11:28:27,073 - ux_test - INFO - 测试完成: 图像预览交互测试 - 通过
2025-07-26 11:28:27,073 - ux_test - INFO - 执行测试: 文件管理测试
2025-07-26 11:28:27,076 - ux_test - INFO - 文件管理功能测试完成
2025-07-26 11:28:27,077 - ux_test - INFO - 测试完成: 文件管理测试 - 通过
2025-07-26 11:28:27,077 - ux_test - INFO - 执行测试: 帮助系统测试
2025-07-26 11:28:27,078 - ux_test - INFO - 帮助系统测试完成
2025-07-26 11:28:27,079 - ux_test - INFO - 测试完成: 帮助系统测试 - 通过
2025-07-26 11:28:27,082 - ux_test - INFO - 执行测试: 响应性能测试
2025-07-26 11:28:27,101 - enhanced_image_viewer - INFO - 图像加载成功: 800x600
2025-07-26 11:28:27,102 - ux_test - INFO - 响应性能测试完成
2025-07-26 11:28:27,102 - ux_test - INFO - 测试完成: 响应性能测试 - 通过
2025-07-26 11:28:27,104 - ux_test - INFO - 执行测试: 错误处理测试
2025-07-26 11:28:27,105 - enhanced_image_viewer - ERROR - 加载图像失败: not enough values to unpack (expected 2, got 1)
2025-07-26 11:28:27,106 - ux_test - INFO - 错误处理测试完成
2025-07-26 11:28:27,107 - ux_test - INFO - 测试完成: 错误处理测试 - 通过
2025-07-26 11:28:27,107 - ux_test - INFO - 执行测试: 可访问性测试
2025-07-26 11:28:27,109 - ux_test - INFO - 可访问性测试完成
2025-07-26 11:28:27,110 - ux_test - INFO - 测试完成: 可访问性测试 - 通过
2025-07-26 11:28:27,112 - ux_test - INFO - 测试报告已生成: temp/ux_test_report.md
2025-07-26 11:28:27,186 - ux_test - INFO - 测试资源清理完成
2025-07-26 11:37:42,175 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:37:42,195 - help_system - INFO - 帮助系统初始化完成
2025-07-26 11:37:42,197 - main - INFO - ==================================================
2025-07-26 11:37:42,198 - main - INFO - 屏幕识别与自动化GUI应用程序启动
2025-07-26 11:37:42,198 - main - INFO - ==================================================
2025-07-26 11:37:43,940 - main - INFO - 所有依赖包检查通过
2025-07-26 11:37:43,941 - main - INFO - 目录结构检查完成
2025-07-26 11:37:43,941 - main - INFO - 启动GUI界面...
2025-07-26 11:37:44,082 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:37:45,004 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:37:45,004 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:37:45,005 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:37:45,005 - screen - INFO - ScreenCapture初始化完成
2025-07-26 11:37:45,005 - match - INFO - TemplateMatch初始化完成
2025-07-26 11:37:45,006 - action - INFO - ActionExecutor初始化完成
2025-07-26 11:37:45,007 - file_manager - INFO - 目录结构初始化完成: data
2025-07-26 11:37:45,008 - file_manager - INFO - 文件管理器初始化完成
2025-07-26 11:37:45,015 - enhanced_toolbar - INFO - 增强工具栏初始化完成
2025-07-26 11:37:45,028 - gui - INFO - 增强工具栏创建完成
2025-07-26 11:37:45,047 - enhanced_image_viewer - INFO - 增强图像预览器初始化完成
2025-07-26 11:37:45,050 - gui - INFO - 快捷键绑定完成
2025-07-26 11:37:45,053 - gui - INFO - 主窗口初始化完成
2025-07-26 11:38:04,381 - region_selector - INFO - 开始区域选择
2025-07-26 11:38:04,964 - region_selector - INFO - 区域选择窗口已创建
2025-07-26 11:38:05,634 - region_selector - INFO - 选择区域: (532, 239, 43, 60)
2025-07-26 11:38:07,143 - region_selector - INFO - 确认选择区域: (532, 239, 43, 60)
2025-07-26 11:38:07,170 - enhanced_image_viewer - INFO - 图像加载成功: 43x60
2025-07-26 11:38:07,225 - region_selector - INFO - 区域选择器已清理
2025-07-26 11:38:14,033 - gui - INFO - 图像点击: (29, 49)
2025-07-26 11:38:27,591 - ocr - INFO - OCR识别完成，找到0个文本区域
2025-07-26 11:38:27,592 - ocr - WARNING - 未识别到任何文本
2025-07-26 11:38:33,781 - enhanced_image_viewer - INFO - 图像加载成功: 1600x900
2025-07-26 11:39:49,956 - ocr - INFO - OCR识别完成，找到96个文本区域
2025-07-26 11:39:49,957 - ocr - INFO - 搜索完成，找到4个匹配结果
2025-07-26 11:39:49,958 - ocr - INFO - 高级搜索完成，找到4个匹配结果
2025-07-26 11:56:07,236 - action - INFO - 临时文件清理完成
2025-07-26 11:56:07,317 - main - INFO - 应用程序正常退出
2025-07-26 11:57:29,072 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:57:31,916 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:57:31,917 - ocr - INFO - OCREngine初始化完成
2025-07-26 11:57:31,917 - search_performance - INFO - 开始性能测试...
2025-07-26 11:57:31,918 - search_performance - INFO - 创建测试图像...
2025-07-26 11:57:31,957 - search_performance - INFO - 测试图像创建完成
2025-07-26 11:57:31,958 - search_performance - INFO - small: (400, 800) - 0.01MB
2025-07-26 11:57:31,958 - search_performance - INFO - medium: (800, 1200) - 0.03MB
2025-07-26 11:57:31,959 - search_performance - INFO - large: (1200, 1920) - 0.05MB
2025-07-26 11:57:31,959 - search_performance - INFO - 测试图像大小: small
2025-07-26 11:57:42,778 - ocr - INFO - OCR识别完成，找到1个文本区域
2025-07-26 11:57:42,779 - search_performance - INFO -   测试搜索: OCR (模式: exact)
2025-07-26 11:57:42,780 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,780 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,781 - search_performance - INFO -   测试搜索: 用户名 (模式: exact)
2025-07-26 11:57:42,782 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,782 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,782 - search_performance - INFO -   测试搜索: Login (模式: exact)
2025-07-26 11:57:42,783 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,784 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,785 - search_performance - INFO -   测试搜索: 设置 (模式: exact)
2025-07-26 11:57:42,786 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,786 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,787 - search_performance - INFO -   测试搜索: admin (模式: exact)
2025-07-26 11:57:42,788 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,788 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,789 - search_performance - INFO -   测试搜索: OCR (模式: contains)
2025-07-26 11:57:42,795 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,799 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,801 - search_performance - INFO -   测试搜索: 用户名 (模式: contains)
2025-07-26 11:57:42,802 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,803 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,805 - search_performance - INFO -   测试搜索: Login (模式: contains)
2025-07-26 11:57:42,806 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,807 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,808 - search_performance - INFO -   测试搜索: 设置 (模式: contains)
2025-07-26 11:57:42,809 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,810 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,813 - search_performance - INFO -   测试搜索: admin (模式: contains)
2025-07-26 11:57:42,815 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,815 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,816 - search_performance - INFO -   测试搜索: OCR (模式: fuzzy)
2025-07-26 11:57:42,817 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,818 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,819 - search_performance - INFO -   测试搜索: 用户名 (模式: fuzzy)
2025-07-26 11:57:42,820 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,820 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,821 - search_performance - INFO -   测试搜索: Login (模式: fuzzy)
2025-07-26 11:57:42,822 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,823 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,824 - search_performance - INFO -   测试搜索: 设置 (模式: fuzzy)
2025-07-26 11:57:42,825 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,825 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,826 - search_performance - INFO -   测试搜索: admin (模式: fuzzy)
2025-07-26 11:57:42,829 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,830 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,831 - search_performance - INFO -   测试搜索: OCR (模式: regex)
2025-07-26 11:57:42,833 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,833 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,834 - search_performance - INFO -   测试搜索: 用户名 (模式: regex)
2025-07-26 11:57:42,835 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,835 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,836 - search_performance - INFO -   测试搜索: Login (模式: regex)
2025-07-26 11:57:42,837 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,837 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,838 - search_performance - INFO -   测试搜索: 设置 (模式: regex)
2025-07-26 11:57:42,839 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,839 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,840 - search_performance - INFO -   测试搜索: admin (模式: regex)
2025-07-26 11:57:42,840 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,841 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,841 - search_performance - INFO -   测试搜索: OCR (模式: similarity)
2025-07-26 11:57:42,842 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,843 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,843 - search_performance - INFO -   测试搜索: 用户名 (模式: similarity)
2025-07-26 11:57:42,844 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,845 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,845 - search_performance - INFO -   测试搜索: Login (模式: similarity)
2025-07-26 11:57:42,846 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,847 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,847 - search_performance - INFO -   测试搜索: 设置 (模式: similarity)
2025-07-26 11:57:42,848 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,849 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,849 - search_performance - INFO -   测试搜索: admin (模式: similarity)
2025-07-26 11:57:42,850 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:57:42,851 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:57:42,851 - search_performance - INFO - 测试图像大小: medium
2025-07-26 11:58:00,085 - ocr - INFO - OCR识别完成，找到10个文本区域
2025-07-26 11:58:00,085 - search_performance - INFO -   测试搜索: OCR (模式: exact)
2025-07-26 11:58:00,086 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,087 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,087 - search_performance - INFO -   测试搜索: 用户名 (模式: exact)
2025-07-26 11:58:00,088 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,089 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,089 - search_performance - INFO -   测试搜索: Login (模式: exact)
2025-07-26 11:58:00,090 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,090 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,091 - search_performance - INFO -   测试搜索: 设置 (模式: exact)
2025-07-26 11:58:00,092 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,092 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,092 - search_performance - INFO -   测试搜索: admin (模式: exact)
2025-07-26 11:58:00,093 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,094 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,094 - search_performance - INFO -   测试搜索: OCR (模式: contains)
2025-07-26 11:58:00,095 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:00,096 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:00,110 - search_performance - INFO -   测试搜索: 用户名 (模式: contains)
2025-07-26 11:58:00,111 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,111 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,112 - search_performance - INFO -   测试搜索: Login (模式: contains)
2025-07-26 11:58:00,113 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:00,114 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:00,126 - search_performance - INFO -   测试搜索: 设置 (模式: contains)
2025-07-26 11:58:00,127 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,128 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,128 - search_performance - INFO -   测试搜索: admin (模式: contains)
2025-07-26 11:58:00,130 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:00,130 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:00,141 - search_performance - INFO -   测试搜索: OCR (模式: fuzzy)
2025-07-26 11:58:00,142 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,143 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,143 - search_performance - INFO -   测试搜索: 用户名 (模式: fuzzy)
2025-07-26 11:58:00,145 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,145 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,146 - search_performance - INFO -   测试搜索: Login (模式: fuzzy)
2025-07-26 11:58:00,147 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,147 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,148 - search_performance - INFO -   测试搜索: 设置 (模式: fuzzy)
2025-07-26 11:58:00,149 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,150 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,150 - search_performance - INFO -   测试搜索: admin (模式: fuzzy)
2025-07-26 11:58:00,151 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,152 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,152 - search_performance - INFO -   测试搜索: OCR (模式: regex)
2025-07-26 11:58:00,153 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:00,154 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:00,156 - search_performance - INFO -   测试搜索: 用户名 (模式: regex)
2025-07-26 11:58:00,157 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,157 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,158 - search_performance - INFO -   测试搜索: Login (模式: regex)
2025-07-26 11:58:00,159 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:00,159 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:00,172 - search_performance - INFO -   测试搜索: 设置 (模式: regex)
2025-07-26 11:58:00,173 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,174 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,174 - search_performance - INFO -   测试搜索: admin (模式: regex)
2025-07-26 11:58:00,175 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:00,175 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:00,188 - search_performance - INFO -   测试搜索: OCR (模式: similarity)
2025-07-26 11:58:00,189 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,190 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,190 - search_performance - INFO -   测试搜索: 用户名 (模式: similarity)
2025-07-26 11:58:00,191 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,192 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,192 - search_performance - INFO -   测试搜索: Login (模式: similarity)
2025-07-26 11:58:00,193 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,194 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,194 - search_performance - INFO -   测试搜索: 设置 (模式: similarity)
2025-07-26 11:58:00,195 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,196 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,196 - search_performance - INFO -   测试搜索: admin (模式: similarity)
2025-07-26 11:58:00,197 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:00,197 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:00,198 - search_performance - INFO - 测试图像大小: large
2025-07-26 11:58:21,574 - ocr - INFO - OCR识别完成，找到14个文本区域
2025-07-26 11:58:21,575 - search_performance - INFO -   测试搜索: OCR (模式: exact)
2025-07-26 11:58:21,576 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,576 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,577 - search_performance - INFO -   测试搜索: 用户名 (模式: exact)
2025-07-26 11:58:21,578 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,578 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,579 - search_performance - INFO -   测试搜索: Login (模式: exact)
2025-07-26 11:58:21,580 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,580 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,581 - search_performance - INFO -   测试搜索: 设置 (模式: exact)
2025-07-26 11:58:21,581 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,582 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,583 - search_performance - INFO -   测试搜索: admin (模式: exact)
2025-07-26 11:58:21,584 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,584 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,585 - search_performance - INFO -   测试搜索: OCR (模式: contains)
2025-07-26 11:58:21,586 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:21,586 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:21,598 - search_performance - INFO -   测试搜索: 用户名 (模式: contains)
2025-07-26 11:58:21,599 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,599 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,600 - search_performance - INFO -   测试搜索: Login (模式: contains)
2025-07-26 11:58:21,601 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:21,602 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:21,614 - search_performance - INFO -   测试搜索: 设置 (模式: contains)
2025-07-26 11:58:21,615 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,615 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,616 - search_performance - INFO -   测试搜索: admin (模式: contains)
2025-07-26 11:58:21,617 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:21,617 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:21,629 - search_performance - INFO -   测试搜索: OCR (模式: fuzzy)
2025-07-26 11:58:21,630 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,631 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,631 - search_performance - INFO -   测试搜索: 用户名 (模式: fuzzy)
2025-07-26 11:58:21,632 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,633 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,634 - search_performance - INFO -   测试搜索: Login (模式: fuzzy)
2025-07-26 11:58:21,635 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,636 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,636 - search_performance - INFO -   测试搜索: 设置 (模式: fuzzy)
2025-07-26 11:58:21,637 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,638 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,638 - search_performance - INFO -   测试搜索: admin (模式: fuzzy)
2025-07-26 11:58:21,640 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,641 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,641 - search_performance - INFO -   测试搜索: OCR (模式: regex)
2025-07-26 11:58:21,642 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:21,643 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:21,645 - search_performance - INFO -   测试搜索: 用户名 (模式: regex)
2025-07-26 11:58:21,646 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,647 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,647 - search_performance - INFO -   测试搜索: Login (模式: regex)
2025-07-26 11:58:21,648 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:21,649 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:21,660 - search_performance - INFO -   测试搜索: 设置 (模式: regex)
2025-07-26 11:58:21,661 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,662 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,662 - search_performance - INFO -   测试搜索: admin (模式: regex)
2025-07-26 11:58:21,663 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 11:58:21,664 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 11:58:21,676 - search_performance - INFO -   测试搜索: OCR (模式: similarity)
2025-07-26 11:58:21,677 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,677 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,678 - search_performance - INFO -   测试搜索: 用户名 (模式: similarity)
2025-07-26 11:58:21,679 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,679 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,680 - search_performance - INFO -   测试搜索: Login (模式: similarity)
2025-07-26 11:58:21,681 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,682 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,682 - search_performance - INFO -   测试搜索: 设置 (模式: similarity)
2025-07-26 11:58:21,683 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,684 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,684 - search_performance - INFO -   测试搜索: admin (模式: similarity)
2025-07-26 11:58:21,685 - ocr - INFO - 搜索完成，找到0个匹配结果
2025-07-26 11:58:21,686 - ocr - INFO - 高级搜索完成，找到0个匹配结果
2025-07-26 11:58:21,689 - search_performance - INFO - 性能测试报告已生成: temp/search_performance_report.md
2025-07-26 11:58:21,690 - search_performance - INFO - 详细数据已保存: temp/search_performance_data.json
2025-07-26 11:59:49,955 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 11:59:49,973 - chinese_display_test - INFO - 使用字体: C:/Windows/Fonts/msyh.ttc
2025-07-26 11:59:52,712 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 11:59:52,713 - ocr - INFO - OCREngine初始化完成
2025-07-26 12:00:05,112 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 12:00:05,113 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 12:00:05,114 - ocr - INFO - 高级搜索完成，找到1个匹配结果
2025-07-26 12:02:29,813 - performance_monitor - INFO - 性能监控器已初始化
2025-07-26 12:02:29,831 - chinese_display_test - INFO - 使用字体: C:/Windows/Fonts/msyh.ttc
2025-07-26 12:02:32,417 - ocr - INFO - PaddleOCR初始化成功
2025-07-26 12:02:32,417 - ocr - INFO - OCREngine初始化完成
2025-07-26 12:02:43,420 - ocr - INFO - OCR识别完成，找到7个文本区域
2025-07-26 12:02:43,421 - ocr - INFO - 搜索完成，找到1个匹配结果
2025-07-26 12:02:43,422 - ocr - INFO - 高级搜索完成，找到1个匹配结果
