"""
用户体验测试脚本
全面测试GUI界面的功能完整性和用户体验
"""

import tkinter as tk
import time
import threading
from typing import Dict, List, Any
import cv2
import numpy as np

from gui.main_window import MainWindow
from utils.enhanced_toolbar import EnhancedToolbar
from utils.file_manager import FileManager
from utils.help_system import help_system
from utils.logger import get_logger

logger = get_logger("ux_test")


class UserExperienceTest:
    """用户体验测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_results = {}
        self.main_window = None
        self.test_image = None
        
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有用户体验测试"""
        logger.info("开始用户体验测试...")
        
        # 创建测试图像
        self._create_test_image()
        
        # 创建主窗口
        self._create_main_window()
        
        # 运行测试
        tests = [
            ("界面启动测试", self._test_interface_startup),
            ("工具栏功能测试", self._test_toolbar_functionality),
            ("图像预览交互测试", self._test_image_viewer_interaction),
            ("文件管理测试", self._test_file_management),
            ("帮助系统测试", self._test_help_system),
            ("响应性能测试", self._test_responsiveness),
            ("错误处理测试", self._test_error_handling),
            ("可访问性测试", self._test_accessibility)
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"执行测试: {test_name}")
                result = test_func()
                self.test_results[test_name] = result
                logger.info(f"测试完成: {test_name} - {'通过' if result['passed'] else '失败'}")
            except Exception as e:
                logger.error(f"测试失败: {test_name} - {e}")
                self.test_results[test_name] = {
                    'passed': False,
                    'error': str(e),
                    'details': {}
                }
        
        # 生成测试报告
        self._generate_test_report()
        
        # 清理
        self._cleanup()
        
        return self.test_results
    
    def _create_test_image(self):
        """创建测试图像"""
        try:
            # 创建包含多种元素的测试图像
            image = np.ones((600, 800, 3), dtype=np.uint8) * 255
            
            # 添加文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            texts = [
                ("用户体验测试图像", (200, 50), 1.2, (0, 0, 0)),
                ("登录按钮", (50, 150), 1.0, (0, 0, 255)),
                ("用户名输入框", (50, 200), 0.8, (0, 128, 0)),
                ("密码输入框", (50, 250), 0.8, (0, 128, 0)),
                ("提交表单", (400, 150), 1.0, (255, 0, 0)),
                ("取消操作", (400, 200), 0.9, (128, 128, 128)),
                ("帮助链接", (400, 250), 0.7, (0, 0, 200)),
                ("版本信息 v2.0", (50, 350), 0.6, (100, 100, 100)),
                ("联系我们", (400, 350), 0.8, (0, 150, 150)),
                ("设置选项", (50, 450), 0.9, (128, 0, 128)),
                ("退出应用", (400, 450), 0.9, (200, 0, 0)),
                ("搜索功能", (200, 500), 0.8, (0, 100, 200))
            ]
            
            for text, pos, scale, color in texts:
                cv2.putText(image, text, pos, font, scale, color, 2)
            
            # 添加一些几何形状
            cv2.rectangle(image, (30, 130), (770, 480), (200, 200, 200), 2)
            cv2.line(image, (400, 130), (400, 480), (200, 200, 200), 1)
            cv2.circle(image, (100, 550), 20, (255, 0, 0), 2)
            cv2.circle(image, (200, 550), 20, (0, 255, 0), 2)
            cv2.circle(image, (300, 550), 20, (0, 0, 255), 2)
            
            self.test_image = image
            cv2.imwrite("temp/ux_test_image.png", image)
            logger.info("测试图像创建完成")
            
        except Exception as e:
            logger.error(f"创建测试图像失败: {e}")
            raise
    
    def _create_main_window(self):
        """创建主窗口"""
        try:
            self.main_window = MainWindow()
            # 加载测试图像
            if self.test_image is not None:
                self.main_window.display_image(self.test_image)
            logger.info("主窗口创建完成")
        except Exception as e:
            logger.error(f"创建主窗口失败: {e}")
            raise
    
    def _test_interface_startup(self) -> Dict[str, Any]:
        """测试界面启动"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            start_time = time.time()
            
            # 检查主窗口是否存在
            if not self.main_window or not self.main_window.root:
                result['passed'] = False
                result['details']['error'] = "主窗口未创建"
                return result
            
            # 检查关键组件
            components = {
                'enhanced_toolbar': self.main_window.enhanced_toolbar,
                'enhanced_image_viewer': self.main_window.enhanced_image_viewer,
                'file_manager': self.main_window.file_manager,
                'status_var': getattr(self.main_window, 'status_var', None)
            }
            
            for name, component in components.items():
                if component is None:
                    result['details'][f'missing_{name}'] = True
                    result['passed'] = False
                else:
                    result['details'][f'has_{name}'] = True
            
            startup_time = time.time() - start_time
            result['metrics']['startup_time'] = startup_time
            
            # 检查窗口响应性
            self.main_window.root.update()
            result['details']['window_responsive'] = True
            
            logger.info(f"界面启动测试完成，耗时: {startup_time:.3f}秒")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_toolbar_functionality(self) -> Dict[str, Any]:
        """测试工具栏功能"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            if not self.main_window.enhanced_toolbar:
                result['passed'] = False
                result['details']['error'] = "增强工具栏未创建"
                return result
            
            # 检查按钮组
            toolbar = self.main_window.enhanced_toolbar
            button_groups = toolbar.button_groups
            
            result['details']['button_groups_count'] = len(button_groups)
            result['details']['button_groups'] = list(button_groups.keys())
            
            # 检查工具提示
            tooltip_count = len(toolbar.tooltips)
            result['details']['tooltips_count'] = tooltip_count
            
            # 检查按钮可用性
            enabled_buttons = 0
            total_buttons = len(toolbar.buttons)
            
            for button_text, button in toolbar.buttons.items():
                if button['state'] != tk.DISABLED:
                    enabled_buttons += 1
            
            result['details']['total_buttons'] = total_buttons
            result['details']['enabled_buttons'] = enabled_buttons
            result['metrics']['button_availability_ratio'] = enabled_buttons / total_buttons if total_buttons > 0 else 0
            
            # 检查按钮分组逻辑
            expected_groups = ["图像获取", "识别分析", "自动化操作", "工具"]
            missing_groups = [g for g in expected_groups if g not in button_groups]
            result['details']['missing_groups'] = missing_groups
            
            if missing_groups:
                result['passed'] = False
            
            logger.info("工具栏功能测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_image_viewer_interaction(self) -> Dict[str, Any]:
        """测试图像预览交互"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            if not self.main_window.enhanced_image_viewer:
                result['passed'] = False
                result['details']['error'] = "增强图像预览器未创建"
                return result
            
            viewer = self.main_window.enhanced_image_viewer
            
            # 检查图像是否加载
            if viewer.current_image is None:
                result['passed'] = False
                result['details']['error'] = "测试图像未加载"
                return result
            
            # 检查缩放功能
            original_scale = viewer.scale_factor
            viewer.zoom_in()
            after_zoom_in = viewer.scale_factor
            viewer.zoom_out()
            after_zoom_out = viewer.scale_factor
            
            result['details']['zoom_in_works'] = after_zoom_in > original_scale
            result['details']['zoom_out_works'] = after_zoom_out < after_zoom_in
            
            # 检查适应窗口功能
            viewer.fit_to_window()
            result['details']['fit_to_window_works'] = True
            
            # 检查原始尺寸功能
            viewer.original_size()
            result['details']['original_size_works'] = viewer.scale_factor == 1.0
            
            # 检查标注功能
            test_annotations = [{
                'type': 'rectangle',
                'bbox': (100, 100, 50, 30),
                'color': (255, 0, 0),
                'thickness': 2
            }]
            viewer.add_annotations(test_annotations)
            result['details']['annotations_work'] = len(viewer.annotations) > 0
            
            # 检查工具栏控件
            toolbar_widgets = ['zoom_combo', 'show_annotations_var', 'coord_label', 'info_label']
            for widget_name in toolbar_widgets:
                if hasattr(viewer, widget_name):
                    result['details'][f'has_{widget_name}'] = True
                else:
                    result['details'][f'missing_{widget_name}'] = True
                    result['passed'] = False
            
            logger.info("图像预览交互测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_file_management(self) -> Dict[str, Any]:
        """测试文件管理功能"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            file_manager = self.main_window.file_manager
            
            # 检查目录结构
            base_dir = file_manager.base_dir
            result['details']['base_dir_exists'] = base_dir.exists()
            
            # 检查分类目录
            categories = file_manager.categories
            result['details']['categories_count'] = len(categories)
            result['details']['categories'] = list(categories.keys())
            
            # 测试文件名生成
            test_filename = file_manager.generate_filename('screenshots', 'fullscreen', 'test')
            result['details']['filename_generation_works'] = bool(test_filename)
            
            # 测试存储信息获取
            storage_info = file_manager.get_storage_info()
            result['details']['storage_info_available'] = bool(storage_info)
            
            # 测试文件列表获取
            files = file_manager.get_files()
            result['details']['file_list_works'] = isinstance(files, list)
            result['metrics']['total_files'] = len(files)
            
            logger.info("文件管理功能测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_help_system(self) -> Dict[str, Any]:
        """测试帮助系统"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            # 检查帮助数据
            help_data = help_system.help_data
            result['details']['help_topics_count'] = len(help_data)
            result['details']['help_topics'] = list(help_data.keys())
            
            # 检查工具提示
            test_components = ['fullscreen_screenshot', 'ocr_recognition', 'text_search']
            tooltip_count = 0
            for component in test_components:
                tooltip = help_system.get_tooltip_text(component)
                if tooltip and tooltip != "暂无帮助信息":
                    tooltip_count += 1
            
            result['details']['tooltips_available'] = tooltip_count
            result['metrics']['tooltip_coverage'] = tooltip_count / len(test_components)
            
            # 检查帮助内容质量
            for topic, content in help_data.items():
                if 'content' not in content or len(content['content']) < 100:
                    result['details'][f'insufficient_content_{topic}'] = True
                    result['passed'] = False
            
            logger.info("帮助系统测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_responsiveness(self) -> Dict[str, Any]:
        """测试响应性能"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            # 测试界面更新响应时间
            start_time = time.time()
            self.main_window.root.update()
            update_time = time.time() - start_time
            result['metrics']['ui_update_time'] = update_time
            
            # 测试图像加载响应时间
            start_time = time.time()
            self.main_window.display_image(self.test_image)
            image_load_time = time.time() - start_time
            result['metrics']['image_load_time'] = image_load_time
            
            # 检查响应时间是否在可接受范围内
            result['details']['ui_responsive'] = update_time < 0.1
            result['details']['image_load_responsive'] = image_load_time < 1.0
            
            if update_time >= 0.1 or image_load_time >= 1.0:
                result['passed'] = False
            
            logger.info("响应性能测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            # 测试无效图像处理
            try:
                invalid_image = np.array([])
                self.main_window.display_image(invalid_image)
                result['details']['handles_invalid_image'] = True
            except Exception:
                result['details']['handles_invalid_image'] = False
            
            # 测试空搜索处理
            try:
                self.main_window.search_var.set("")
                # 这里应该不会崩溃
                result['details']['handles_empty_search'] = True
            except Exception:
                result['details']['handles_empty_search'] = False
                result['passed'] = False
            
            logger.info("错误处理测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _test_accessibility(self) -> Dict[str, Any]:
        """测试可访问性"""
        result = {
            'passed': True,
            'details': {},
            'metrics': {}
        }
        
        try:
            # 检查快捷键支持
            # 这里可以检查是否定义了快捷键
            result['details']['keyboard_shortcuts_defined'] = False  # 需要实际实现
            
            # 检查工具提示
            if self.main_window.enhanced_toolbar:
                tooltip_count = len(self.main_window.enhanced_toolbar.tooltips)
                result['details']['tooltips_count'] = tooltip_count
                result['details']['has_tooltips'] = tooltip_count > 0
            
            # 检查状态反馈
            status_var = getattr(self.main_window, 'status_var', None)
            result['details']['has_status_feedback'] = status_var is not None
            
            # 检查颜色对比度（简单检查）
            result['details']['color_contrast_ok'] = True  # 需要更详细的实现
            
            logger.info("可访问性测试完成")
            
        except Exception as e:
            result['passed'] = False
            result['details']['exception'] = str(e)
        
        return result
    
    def _generate_test_report(self):
        """生成测试报告"""
        try:
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results.values() if result.get('passed', False))
            
            report = f"""
# 用户体验测试报告

## 测试概览
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 失败测试: {total_tests - passed_tests}
- 通过率: {passed_tests / total_tests * 100:.1f}%

## 详细结果
"""
            
            for test_name, result in self.test_results.items():
                status = "✅ 通过" if result.get('passed', False) else "❌ 失败"
                report += f"\n### {test_name}: {status}\n"
                
                if 'details' in result:
                    for key, value in result['details'].items():
                        report += f"- {key}: {value}\n"
                
                if 'metrics' in result:
                    report += "**性能指标:**\n"
                    for key, value in result['metrics'].items():
                        if isinstance(value, float):
                            report += f"- {key}: {value:.3f}\n"
                        else:
                            report += f"- {key}: {value}\n"
            
            # 保存报告
            with open("temp/ux_test_report.md", "w", encoding="utf-8") as f:
                f.write(report)
            
            logger.info("测试报告已生成: temp/ux_test_report.md")
            
        except Exception as e:
            logger.error(f"生成测试报告失败: {e}")
    
    def _cleanup(self):
        """清理测试资源"""
        try:
            if self.main_window and self.main_window.root:
                self.main_window.root.quit()
                self.main_window.root.destroy()
            logger.info("测试资源清理完成")
        except Exception as e:
            logger.warning(f"清理测试资源失败: {e}")


def main():
    """主测试函数"""
    import os
    os.makedirs("temp", exist_ok=True)
    
    print("开始用户体验测试...")
    
    test = UserExperienceTest()
    results = test.run_all_tests()
    
    # 输出结果摘要
    total = len(results)
    passed = sum(1 for r in results.values() if r.get('passed', False))
    
    print(f"\n测试完成!")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed / total * 100:.1f}%")
    
    if passed == total:
        print("🎉 所有用户体验测试通过！")
    else:
        print("⚠️ 部分测试失败，请查看详细报告")
    
    print("\n详细报告已保存到: temp/ux_test_report.md")


if __name__ == "__main__":
    main()
