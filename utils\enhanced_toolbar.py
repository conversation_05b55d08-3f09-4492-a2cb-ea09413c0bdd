"""
增强的工具栏管理器
提供更好的按钮分组、工具提示和视觉效果
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Callable, Optional, Tuple
from dataclasses import dataclass

from utils.logger import get_logger

logger = get_logger("enhanced_toolbar")


@dataclass
class ToolbarButton:
    """工具栏按钮配置"""
    text: str
    command: Callable
    tooltip: str
    icon: Optional[str] = None
    shortcut: Optional[str] = None
    enabled: bool = True
    style: str = "default"


@dataclass
class ToolbarGroup:
    """工具栏按钮组"""
    name: str
    buttons: List[ToolbarButton]
    collapsible: bool = False
    expanded: bool = True


class EnhancedToolbar:
    """增强的工具栏类"""
    
    def __init__(self, parent: tk.Widget):
        """
        初始化增强工具栏
        
        Args:
            parent: 父窗口组件
        """
        self.parent = parent
        self.toolbar_frame = None
        self.button_groups = {}
        self.buttons = {}
        self.tooltips = {}
        
        # 样式配置
        self.styles = {
            'default': {'relief': 'raised', 'borderwidth': 1},
            'primary': {'relief': 'raised', 'borderwidth': 2},
            'secondary': {'relief': 'flat', 'borderwidth': 1},
            'danger': {'relief': 'raised', 'borderwidth': 1}
        }
        
        self._create_toolbar()
        logger.info("增强工具栏初始化完成")
    
    def _create_toolbar(self):
        """创建工具栏框架"""
        self.toolbar_frame = ttk.Frame(self.parent)
        self.toolbar_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
    
    def add_group(self, group: ToolbarGroup):
        """
        添加按钮组
        
        Args:
            group: 按钮组配置
        """
        try:
            # 创建组框架
            group_frame = ttk.LabelFrame(self.toolbar_frame, text=group.name, padding=5)
            group_frame.pack(side=tk.LEFT, padx=5, pady=2, fill=tk.Y)
            
            # 创建按钮容器
            button_container = ttk.Frame(group_frame)
            button_container.pack(fill=tk.BOTH, expand=True)
            
            # 添加按钮
            group_buttons = []
            for button_config in group.buttons:
                button = self._create_button(button_container, button_config)
                if button:
                    group_buttons.append(button)
            
            self.button_groups[group.name] = {
                'frame': group_frame,
                'container': button_container,
                'buttons': group_buttons,
                'config': group
            }
            
            logger.debug(f"添加按钮组: {group.name}, 包含{len(group_buttons)}个按钮")
            
        except Exception as e:
            logger.error(f"添加按钮组失败: {e}")
    
    def _create_button(self, parent: tk.Widget, config: ToolbarButton) -> Optional[ttk.Button]:
        """
        创建单个按钮
        
        Args:
            parent: 父容器
            config: 按钮配置
            
        Returns:
            创建的按钮组件
        """
        try:
            # 创建按钮，如果command为None则使用空函数
            command = config.command if config.command is not None else lambda: None
            button = ttk.Button(
                parent,
                text=config.text,
                command=command,
                state=tk.NORMAL if config.enabled else tk.DISABLED
            )
            
            # 设置按钮样式 (ttk.Button不支持relief和borderwidth)
            # 注意：ttk.Button使用主题样式，不支持传统的tk.Button样式选项
            
            # 打包按钮
            button.pack(side=tk.LEFT, padx=2, pady=1)
            
            # 添加工具提示
            if config.tooltip:
                self._add_tooltip(button, config.tooltip, config.shortcut)
            
            # 存储按钮引用
            self.buttons[config.text] = button
            
            return button
            
        except Exception as e:
            logger.error(f"创建按钮失败: {e}")
            return None
    
    def _add_tooltip(self, widget: tk.Widget, text: str, shortcut: Optional[str] = None):
        """
        添加工具提示
        
        Args:
            widget: 目标组件
            text: 提示文本
            shortcut: 快捷键
        """
        try:
            tooltip_text = text
            if shortcut:
                tooltip_text += f"\n快捷键: {shortcut}"
            
            tooltip = ToolTip(widget, tooltip_text)
            self.tooltips[widget] = tooltip
            
        except Exception as e:
            logger.warning(f"添加工具提示失败: {e}")
    
    def enable_button(self, button_text: str):
        """启用按钮"""
        if button_text in self.buttons:
            self.buttons[button_text].configure(state=tk.NORMAL)
    
    def disable_button(self, button_text: str):
        """禁用按钮"""
        if button_text in self.buttons:
            self.buttons[button_text].configure(state=tk.DISABLED)
    
    def update_button_text(self, button_text: str, new_text: str):
        """更新按钮文本"""
        if button_text in self.buttons:
            self.buttons[button_text].configure(text=new_text)
    
    def toggle_group(self, group_name: str):
        """切换按钮组的显示/隐藏"""
        if group_name in self.button_groups:
            group = self.button_groups[group_name]
            if group['config'].collapsible:
                if group['config'].expanded:
                    group['container'].pack_forget()
                    group['config'].expanded = False
                else:
                    group['container'].pack(fill=tk.BOTH, expand=True)
                    group['config'].expanded = True


class ToolTip:
    """工具提示类"""
    
    def __init__(self, widget: tk.Widget, text: str):
        """
        初始化工具提示
        
        Args:
            widget: 目标组件
            text: 提示文本
        """
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        
        # 绑定事件
        self.widget.bind("<Enter>", self._on_enter)
        self.widget.bind("<Leave>", self._on_leave)
        self.widget.bind("<Motion>", self._on_motion)
    
    def _on_enter(self, event):
        """鼠标进入事件"""
        self._show_tooltip(event)
    
    def _on_leave(self, event):
        """鼠标离开事件"""
        self._hide_tooltip()
    
    def _on_motion(self, event):
        """鼠标移动事件"""
        if self.tooltip_window:
            self._update_position(event)
    
    def _show_tooltip(self, event):
        """显示工具提示"""
        try:
            if self.tooltip_window:
                return
            
            # 创建提示窗口
            self.tooltip_window = tk.Toplevel(self.widget)
            self.tooltip_window.wm_overrideredirect(True)
            self.tooltip_window.wm_attributes("-topmost", True)
            
            # 设置窗口样式
            label = tk.Label(
                self.tooltip_window,
                text=self.text,
                background="lightyellow",
                foreground="black",
                relief="solid",
                borderwidth=1,
                font=("Arial", 9),
                justify=tk.LEFT,
                padx=5,
                pady=3
            )
            label.pack()
            
            # 设置位置
            self._update_position(event)
            
        except Exception as e:
            logger.warning(f"显示工具提示失败: {e}")
    
    def _hide_tooltip(self):
        """隐藏工具提示"""
        try:
            if self.tooltip_window:
                self.tooltip_window.destroy()
                self.tooltip_window = None
        except Exception as e:
            logger.warning(f"隐藏工具提示失败: {e}")
    
    def _update_position(self, event):
        """更新提示位置"""
        try:
            if self.tooltip_window:
                x = event.x_root + 10
                y = event.y_root + 10
                self.tooltip_window.wm_geometry(f"+{x}+{y}")
        except Exception as e:
            logger.warning(f"更新工具提示位置失败: {e}")


def create_default_toolbar_config() -> List[ToolbarGroup]:
    """创建默认的工具栏配置"""
    return [
        ToolbarGroup(
            name="图像获取",
            buttons=[
                ToolbarButton("全屏截图", None, "截取整个屏幕\n适用于全屏应用分析", shortcut="Ctrl+1"),
                ToolbarButton("区域截图", None, "截取指定矩形区域\n适用于局部区域分析", shortcut="Ctrl+2"),
                ToolbarButton("交互选择", None, "交互式拖拽选择区域\n提供实时预览和尺寸显示", shortcut="Ctrl+3"),
                ToolbarButton("导入图像", None, "从本地文件导入图像\n支持PNG、JPG、BMP等格式", shortcut="Ctrl+O")
            ]
        ),
        ToolbarGroup(
            name="识别分析",
            buttons=[
                ToolbarButton("OCR识别", None, "识别图像中的文本内容\n支持中英文混合识别", shortcut="Ctrl+R", style="primary"),
                ToolbarButton("文本搜索", None, "在识别结果中搜索特定文本\n支持多种匹配模式", shortcut="Ctrl+F"),
                ToolbarButton("模板匹配", None, "使用模板图像进行匹配\n适用于图标和界面元素识别", shortcut="Ctrl+M")
            ]
        ),
        ToolbarGroup(
            name="自动化操作",
            buttons=[
                ToolbarButton("点击测试", None, "测试自动点击功能\n验证坐标定位准确性", shortcut="Ctrl+T"),
                ToolbarButton("批量操作", None, "执行批量自动化操作\n基于识别结果进行批量点击", shortcut="Ctrl+B")
            ]
        ),
        ToolbarGroup(
            name="工具",
            buttons=[
                ToolbarButton("生成代码", None, "根据当前配置生成Python代码\n支持独立脚本和项目结构", shortcut="Ctrl+G"),
                ToolbarButton("文件管理", None, "管理截图和结果文件\n提供文件浏览和清理功能", shortcut="Ctrl+E"),
                ToolbarButton("配置", None, "打开应用程序配置对话框\n调整OCR和匹配参数", shortcut="Ctrl+,"),
                ToolbarButton("清理", None, "清理临时文件和缓存\n释放存储空间", style="secondary")
            ]
        )
    ]
