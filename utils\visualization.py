"""
可视化工具模块
提供图像标注、边界框绘制、文本高亮等功能
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
import math
from PIL import Image, ImageDraw, ImageFont
import io

from core.ocr import OCRResult, SearchResult
from utils.logger import get_logger

logger = get_logger("visualization")


class ImageAnnotator:
    """图像标注器"""
    
    def __init__(self):
        """初始化标注器"""
        self.default_colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红色
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]
        self.font_scale = 0.6
        self.thickness = 2

        # 初始化中文字体支持
        self.chinese_font = self._load_chinese_font()
        self.font_size = 20

    def _load_chinese_font(self):
        """加载中文字体"""
        font_paths = [
            "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]

        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, self.font_size)
                    logger.info(f"加载中文字体: {font_path}")
                    return font
                except Exception as e:
                    logger.warning(f"加载字体失败 {font_path}: {e}")

        logger.warning("使用默认字体，可能不支持中文")
        return ImageFont.load_default()

    def _draw_text_with_chinese_support(self, image: np.ndarray, text: str,
                                      position: Tuple[int, int],
                                      color: Tuple[int, int, int],
                                      background_color: Optional[Tuple[int, int, int]] = None):
        """使用PIL绘制支持中文的文本"""
        try:
            # 转换为PIL图像
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)

            draw = ImageDraw.Draw(pil_image)

            # 转换颜色格式 (BGR -> RGB)
            rgb_color = (color[2], color[1], color[0])

            # 绘制文本背景
            if background_color:
                rgb_bg_color = (background_color[2], background_color[1], background_color[0])
                bbox = draw.textbbox(position, text, font=self.chinese_font)
                draw.rectangle(bbox, fill=rgb_bg_color)

            # 绘制文本
            draw.text(position, text, fill=rgb_color, font=self.chinese_font)

            # 转换回OpenCV格式
            result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

            # 复制结果到原图像
            image[:] = result_image[:]

        except Exception as e:
            logger.warning(f"使用PIL绘制文本失败: {e}")
            # 回退到OpenCV方法
            cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX,
                       self.font_scale, color, 1)

    def annotate_ocr_results(self, image: np.ndarray,
                           ocr_results: List[OCRResult],
                           show_text: bool = True,
                           show_confidence: bool = True,
                           show_bbox: bool = True) -> np.ndarray:
        """
        标注OCR识别结果
        
        Args:
            image: 输入图像
            ocr_results: OCR结果列表
            show_text: 是否显示文本
            show_confidence: 是否显示置信度
            show_bbox: 是否显示边界框
            
        Returns:
            标注后的图像
        """
        try:
            annotated_image = image.copy()
            
            for i, result in enumerate(ocr_results):
                color = result.highlight_color if result.is_highlighted else self.default_colors[i % len(self.default_colors)]
                
                # 绘制边界框
                if show_bbox:
                    self._draw_bbox(annotated_image, result.bbox, color, self.thickness)
                
                # 绘制文本和置信度
                if show_text or show_confidence:
                    self._draw_text_info(annotated_image, result, color, show_text, show_confidence)
            
            logger.debug(f"标注了{len(ocr_results)}个OCR结果")
            return annotated_image
            
        except Exception as e:
            logger.error(f"标注OCR结果失败: {e}")
            return image
    
    def annotate_search_results(self, image: np.ndarray, 
                              search_results: List[SearchResult],
                              show_match_score: bool = True,
                              show_search_term: bool = True) -> np.ndarray:
        """
        标注搜索结果
        
        Args:
            image: 输入图像
            search_results: 搜索结果列表
            show_match_score: 是否显示匹配分数
            show_search_term: 是否显示搜索词
            
        Returns:
            标注后的图像
        """
        try:
            annotated_image = image.copy()
            
            for i, result in enumerate(search_results):
                ocr_result = result.ocr_result
                color = ocr_result.highlight_color if ocr_result.is_highlighted else self.default_colors[i % len(self.default_colors)]
                
                # 绘制加粗的边界框（搜索结果更突出）
                self._draw_bbox(annotated_image, ocr_result.bbox, color, self.thickness + 1)
                
                # 绘制搜索信息
                self._draw_search_info(annotated_image, result, color, show_match_score, show_search_term)
            
            logger.debug(f"标注了{len(search_results)}个搜索结果")
            return annotated_image
            
        except Exception as e:
            logger.error(f"标注搜索结果失败: {e}")
            return image
    
    def create_result_overlay(self, image: np.ndarray, 
                            search_results: List[SearchResult],
                            overlay_alpha: float = 0.3) -> np.ndarray:
        """
        创建结果覆盖层
        
        Args:
            image: 输入图像
            search_results: 搜索结果列表
            overlay_alpha: 覆盖层透明度
            
        Returns:
            带覆盖层的图像
        """
        try:
            overlay = image.copy()
            
            for i, result in enumerate(search_results):
                ocr_result = result.ocr_result
                color = ocr_result.highlight_color if ocr_result.is_highlighted else self.default_colors[i % len(self.default_colors)]
                
                # 创建填充的矩形
                x, y, width, height = ocr_result.rect
                cv2.rectangle(overlay, (x, y), (x + width, y + height), color, -1)
            
            # 混合原图和覆盖层
            result_image = cv2.addWeighted(image, 1 - overlay_alpha, overlay, overlay_alpha, 0)
            
            # 添加边界框和文本
            result_image = self.annotate_search_results(result_image, search_results)
            
            return result_image
            
        except Exception as e:
            logger.error(f"创建结果覆盖层失败: {e}")
            return image
    
    def _draw_bbox(self, image: np.ndarray, bbox: List[List[int]], 
                   color: Tuple[int, int, int], thickness: int):
        """绘制边界框"""
        try:
            # 转换为numpy数组
            points = np.array(bbox, dtype=np.int32)
            
            # 绘制多边形边界
            cv2.polylines(image, [points], True, color, thickness)
            
            # 绘制角点
            for point in points:
                cv2.circle(image, tuple(point), 3, color, -1)
                
        except Exception as e:
            logger.warning(f"绘制边界框失败: {e}")
    
    def _draw_text_info(self, image: np.ndarray, ocr_result: OCRResult, 
                       color: Tuple[int, int, int], show_text: bool, show_confidence: bool):
        """绘制文本信息"""
        try:
            x, y, width, height = ocr_result.rect
            
            # 准备显示的文本
            display_text = ""
            if show_text:
                display_text = ocr_result.text
            if show_confidence:
                conf_text = f" ({ocr_result.confidence:.2f})"
                display_text += conf_text
            
            if not display_text:
                return
            
            # 计算文本位置（在边界框上方）- 确保坐标为整数
            text_x = int(x)
            text_y = int(max(y - 10, 20))  # 确保不超出图像边界

            # 使用支持中文的文本绘制方法
            self._draw_text_with_chinese_support(
                image, display_text, (text_x, text_y),
                (255, 255, 255), background_color=color
            )
                       
        except Exception as e:
            logger.warning(f"绘制文本信息失败: {e}")
    
    def _draw_search_info(self, image: np.ndarray, search_result: SearchResult,
                         color: Tuple[int, int, int], show_match_score: bool, show_search_term: bool):
        """绘制搜索信息"""
        try:
            ocr_result = search_result.ocr_result
            x, y, width, height = ocr_result.rect
            
            # 准备显示的文本
            info_lines = []
            if show_search_term:
                info_lines.append(f"搜索: {search_result.search_term}")
            if show_match_score:
                info_lines.append(f"匹配: {search_result.match_score:.2f}")
            
            if not info_lines:
                return
            
            # 绘制多行信息
            for i, line in enumerate(info_lines):
                # 确保坐标为整数
                text_x = int(x)
                text_y = int(y + height + 20 + (i * 30))  # 在边界框下方，增加行间距

                # 使用支持中文的文本绘制方法
                self._draw_text_with_chinese_support(
                    image, line, (text_x, text_y),
                    (255, 255, 255), background_color=color
                )
                           
        except Exception as e:
            logger.warning(f"绘制搜索信息失败: {e}")
    
    def create_result_summary_image(self, search_results: List[SearchResult],
                                  image_size: Tuple[int, int] = (400, 300)) -> np.ndarray:
        """
        创建搜索结果摘要图像
        
        Args:
            search_results: 搜索结果列表
            image_size: 图像尺寸
            
        Returns:
            摘要图像
        """
        try:
            width, height = image_size
            summary_image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
            
            if not search_results:
                # 显示"无结果"
                text = "No results found"
                text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 1, 2)[0]
                text_x = (width - text_size[0]) // 2
                text_y = height // 2
                cv2.putText(summary_image, text, (text_x, text_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (128, 128, 128), 2)
                return summary_image
            
            # 显示结果统计
            y_offset = 30
            line_height = 25
            
            # 标题
            title = f"Found {len(search_results)} results"
            cv2.putText(summary_image, title, (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            y_offset += line_height * 2
            
            # 显示前几个结果
            max_display = min(8, len(search_results))
            for i, result in enumerate(search_results[:max_display]):
                color = self.default_colors[i % len(self.default_colors)]
                
                # 绘制颜色标记
                cv2.rectangle(summary_image, (10, y_offset - 15), (25, y_offset), color, -1)
                
                # 显示文本信息
                text = f"{result.search_term}: {result.match_text[:20]}..."
                cv2.putText(summary_image, text, (35, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
                
                y_offset += line_height
            
            if len(search_results) > max_display:
                more_text = f"... and {len(search_results) - max_display} more"
                cv2.putText(summary_image, more_text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)
            
            return summary_image
            
        except Exception as e:
            logger.error(f"创建结果摘要图像失败: {e}")
            # 返回错误图像
            error_image = np.ones(image_size + (3,), dtype=np.uint8) * 128
            cv2.putText(error_image, "Error creating summary", (10, image_size[1]//2),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            return error_image
