"""
OCR文本搜索功能测试脚本
"""

import cv2
import numpy as np
from core.ocr import OCREngine, SearchMode
from core.action import ActionExecutor
from utils.visualization import ImageAnnotator
from utils.logger import get_logger

logger = get_logger("test_ocr_search")


def test_ocr_search_functionality():
    """测试OCR搜索功能"""
    try:
        # 初始化组件
        ocr_engine = OCREngine()
        action_executor = ActionExecutor()
        image_annotator = ImageAnnotator()
        
        logger.info("开始测试OCR搜索功能")
        
        # 创建一个测试图像（包含文本）
        test_image = create_test_image()
        
        # 测试1: 基本OCR识别
        logger.info("测试1: 基本OCR识别")
        ocr_results = ocr_engine.recognize_text(test_image)
        logger.info(f"识别到{len(ocr_results)}个文本区域")
        
        for result in ocr_results:
            logger.info(f"  文本: '{result.text}', 置信度: {result.confidence:.3f}, 中心: {result.center}")
        
        # 测试2: 精确搜索
        logger.info("测试2: 精确搜索")
        search_results = ocr_engine.advanced_search(
            image=test_image,
            search_terms="Hello",
            mode=SearchMode.EXACT
        )
        logger.info(f"精确搜索找到{len(search_results)}个结果")
        
        # 测试3: 包含搜索
        logger.info("测试3: 包含搜索")
        search_results = ocr_engine.advanced_search(
            image=test_image,
            search_terms="ell",
            mode=SearchMode.CONTAINS
        )
        logger.info(f"包含搜索找到{len(search_results)}个结果")
        
        # 测试4: 模糊搜索
        logger.info("测试4: 模糊搜索")
        search_results = ocr_engine.advanced_search(
            image=test_image,
            search_terms="Helo",  # 故意拼错
            mode=SearchMode.FUZZY,
            similarity_threshold=0.7
        )
        logger.info(f"模糊搜索找到{len(search_results)}个结果")
        
        # 测试5: 批量搜索
        logger.info("测试5: 批量搜索")
        search_configs = [
            {'search_term': 'Hello', 'mode': 'exact'},
            {'search_term': 'World', 'mode': 'contains'},
            {'search_term': 'Test', 'mode': 'fuzzy', 'similarity_threshold': 0.8}
        ]
        batch_results = ocr_engine.batch_search(test_image, search_configs)
        logger.info(f"批量搜索完成，找到{len(batch_results)}组结果")
        
        # 测试6: 图像标注
        logger.info("测试6: 图像标注")
        if search_results:
            annotated_image = image_annotator.annotate_search_results(
                test_image, search_results
            )
            cv2.imwrite("temp/test_annotated.png", annotated_image)
            logger.info("标注图像已保存到 temp/test_annotated.png")
        
        # 测试7: 搜索统计
        logger.info("测试7: 搜索统计")
        if search_results:
            stats = ocr_engine.get_search_statistics(search_results)
            logger.info(f"搜索统计: {stats}")
        
        # 测试8: 动作队列
        logger.info("测试8: 动作队列")
        action_queue = action_executor.create_action_queue()
        
        if search_results:
            # 添加点击动作（但不实际执行，只测试队列功能）
            for result in search_results[:2]:  # 只测试前两个结果
                action_queue.add_click_action(result.ocr_result)
                action_queue.add_wait_action(0.5)
        
        logger.info(f"动作队列创建完成，包含{len(action_queue.actions)}个动作")
        
        logger.info("所有测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


def create_test_image():
    """创建包含文本的测试图像"""
    # 创建白色背景
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # 添加一些文本
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # 添加不同大小和位置的文本
    cv2.putText(image, "Hello World", (50, 100), font, 2, (0, 0, 0), 3)
    cv2.putText(image, "Test OCR Search", (50, 200), font, 1.5, (0, 0, 255), 2)
    cv2.putText(image, "Python Automation", (50, 300), font, 1, (0, 255, 0), 2)
    cv2.putText(image, "Screen Recognition", (300, 150), font, 0.8, (255, 0, 0), 1)
    
    # 保存测试图像
    cv2.imwrite("temp/test_image.png", image)
    logger.info("测试图像已创建并保存到 temp/test_image.png")
    
    return image


def test_search_modes():
    """测试不同的搜索模式"""
    try:
        ocr_engine = OCREngine()
        test_image = create_test_image()
        
        # 测试所有搜索模式
        search_modes = [
            (SearchMode.EXACT, "Hello World"),
            (SearchMode.CONTAINS, "World"),
            (SearchMode.FUZZY, "Helo World"),  # 拼写错误
            (SearchMode.SIMILARITY, "Hello"),
            (SearchMode.REGEX, r"Hello.*")
        ]
        
        for mode, search_term in search_modes:
            logger.info(f"测试搜索模式: {mode.value}, 搜索词: '{search_term}'")
            
            try:
                results = ocr_engine.advanced_search(
                    image=test_image,
                    search_terms=search_term,
                    mode=mode,
                    similarity_threshold=0.7
                )
                
                logger.info(f"  找到{len(results)}个结果")
                for result in results:
                    logger.info(f"    匹配: '{result.match_text}', 分数: {result.match_score:.3f}")
                    
            except Exception as e:
                logger.warning(f"  搜索模式{mode.value}测试失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"搜索模式测试失败: {e}")
        return False


if __name__ == "__main__":
    # 确保temp目录存在
    import os
    os.makedirs("temp", exist_ok=True)
    
    print("开始OCR搜索功能测试...")
    
    # 运行基本功能测试
    success1 = test_ocr_search_functionality()
    
    # 运行搜索模式测试
    success2 = test_search_modes()
    
    if success1 and success2:
        print("✅ 所有测试通过！")
    else:
        print("❌ 部分测试失败，请查看日志")
