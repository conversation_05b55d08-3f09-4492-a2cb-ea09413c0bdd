"""
中文字符显示问题测试和修复
测试搜索结果中文字符的显示效果
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path

from core.ocr import OCREngine, SearchMode
from utils.visualization import ImageAnnotator
from utils.logger import get_logger

logger = get_logger("chinese_display_test")


def create_chinese_test_image():
    """创建包含中文的测试图像"""
    # 创建白色背景图像
    image = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 测试文本（中英文混合）
    test_texts = [
        "用户登录 User Login",
        "用户名 Username: admin",
        "密码 Password: 123456",
        "登录按钮 Login Button",
        "注册账号 Register Account",
        "忘记密码 Forgot Password",
        "设置选项 Settings Options",
        "帮助文档 Help Documentation",
        "退出系统 Exit System",
        "搜索功能 Search Function"
    ]
    
    # 使用OpenCV绘制文本（会出现中文显示问题）
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.8
    color = (0, 0, 0)
    thickness = 2
    
    y_offset = 50
    line_height = 50
    
    for i, text in enumerate(test_texts):
        y = y_offset + i * line_height
        cv2.putText(image, text, (50, y), font, font_scale, color, thickness)
    
    return image


def create_chinese_test_image_pil():
    """使用PIL创建支持中文的测试图像"""
    # 创建PIL图像
    pil_image = Image.new('RGB', (800, 600), 'white')
    draw = ImageDraw.Draw(pil_image)
    
    # 尝试加载中文字体
    font_paths = [
        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        "C:/Windows/Fonts/simhei.ttf",  # 黑体
        "/System/Library/Fonts/PingFang.ttc",  # macOS
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
    ]
    
    font = None
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                font = ImageFont.truetype(font_path, 24)
                logger.info(f"使用字体: {font_path}")
                break
            except Exception as e:
                logger.warning(f"加载字体失败 {font_path}: {e}")
    
    if font is None:
        font = ImageFont.load_default()
        logger.warning("使用默认字体，可能不支持中文")
    
    # 测试文本
    test_texts = [
        "用户登录 User Login",
        "用户名 Username: admin",
        "密码 Password: 123456", 
        "登录按钮 Login Button",
        "注册账号 Register Account",
        "忘记密码 Forgot Password",
        "设置选项 Settings Options",
        "帮助文档 Help Documentation",
        "退出系统 Exit System",
        "搜索功能 Search Function"
    ]
    
    # 绘制文本
    y_offset = 30
    line_height = 50
    
    for i, text in enumerate(test_texts):
        y = y_offset + i * line_height
        draw.text((50, y), text, fill='black', font=font)
    
    # 转换为OpenCV格式
    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    return cv_image


def test_chinese_display_issue():
    """测试中文显示问题"""
    print("测试中文字符显示问题...")
    
    # 创建测试目录
    test_dir = Path("temp/chinese_display_test")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # 1. 使用OpenCV创建图像（会有中文显示问题）
    opencv_image = create_chinese_test_image()
    cv2.imwrite(str(test_dir / "opencv_chinese_test.png"), opencv_image)
    print("✅ OpenCV测试图像已保存（中文可能显示为问号）")
    
    # 2. 使用PIL创建图像（支持中文）
    pil_image = create_chinese_test_image_pil()
    cv2.imwrite(str(test_dir / "pil_chinese_test.png"), pil_image)
    print("✅ PIL测试图像已保存（支持中文显示）")
    
    # 3. 测试OCR识别和搜索结果显示
    print("\n测试OCR识别和搜索结果显示...")
    
    try:
        ocr_engine = OCREngine()
        
        # 对PIL图像进行OCR识别
        ocr_results = ocr_engine.recognize_text(pil_image, confidence_threshold=0.8)
        print(f"OCR识别到 {len(ocr_results)} 个文本")
        
        # 测试搜索功能
        search_results = ocr_engine.advanced_search(
            image=pil_image,
            search_terms="用户",
            mode=SearchMode.CONTAINS,
            confidence_threshold=0.8
        )
        print(f"搜索到 {len(search_results)} 个匹配结果")
        
        # 使用修复后的标注方法
        from utils.visualization import ImageAnnotator
        annotator = ImageAnnotator()
        annotated_image = annotator.annotate_search_results(pil_image, search_results)
        cv2.imwrite(str(test_dir / "annotated_fixed.png"), annotated_image)
        print("✅ 修复后的标注方法结果已保存（支持中文显示）")
        
        return search_results, pil_image
        
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        return [], pil_image


def create_fixed_annotator():
    """创建修复中文显示问题的标注器"""
    import os

    class ChineseImageAnnotator:
        """支持中文的图像标注器"""
        
        def __init__(self):
            self.font_paths = [
                "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
            ]
            self.font_size = 20
            self.font = self._load_font()
        
        def _load_font(self):
            """加载中文字体"""
            for font_path in self.font_paths:
                if os.path.exists(font_path):
                    try:
                        return ImageFont.truetype(font_path, self.font_size)
                    except Exception as e:
                        logger.warning(f"加载字体失败 {font_path}: {e}")
            
            logger.warning("使用默认字体")
            return ImageFont.load_default()
        
        def annotate_search_results_fixed(self, image, search_results):
            """修复版本的搜索结果标注"""
            # 转换为PIL图像
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)
            
            draw = ImageDraw.Draw(pil_image)
            
            # 颜色列表
            colors = [
                (0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0),
                (255, 0, 255), (0, 255, 255), (128, 255, 0), (255, 128, 0)
            ]
            
            for i, result in enumerate(search_results):
                color = colors[i % len(colors)]
                ocr_result = result.ocr_result
                
                # 绘制边界框
                bbox = ocr_result.bbox
                bbox_points = [(int(p[0]), int(p[1])) for p in bbox]
                draw.polygon(bbox_points, outline=color, width=2)
                
                # 绘制文本标签（支持中文）
                x = min([p[0] for p in bbox])
                y = min([p[1] for p in bbox]) - 30
                
                # 确保文本位置在图像内
                if y < 0:
                    y = max([p[1] for p in bbox]) + 5
                
                # 绘制文本背景
                text = f"搜索: {result.search_term} -> {result.match_text}"
                bbox_text = draw.textbbox((x, y), text, font=self.font)
                draw.rectangle(bbox_text, fill=color)
                
                # 绘制文本（支持中文）
                draw.text((x, y), text, fill='white', font=self.font)
            
            # 转换回OpenCV格式
            return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    return ChineseImageAnnotator()


def test_fixed_chinese_display():
    """测试修复后的中文显示"""
    print("\n测试修复后的中文显示...")
    
    # 获取测试数据
    search_results, test_image = test_chinese_display_issue()
    
    if not search_results:
        print("❌ 没有搜索结果，跳过修复测试")
        return
    
    # 创建修复版本的标注器
    fixed_annotator = create_fixed_annotator()
    
    # 使用修复版本标注
    fixed_annotated = fixed_annotator.annotate_search_results_fixed(test_image, search_results)
    
    # 保存修复后的结果
    test_dir = Path("temp/chinese_display_test")
    cv2.imwrite(str(test_dir / "annotated_fixed.png"), fixed_annotated)
    print("✅ 修复版本标注结果已保存")
    
    print("\n对比结果:")
    print("- opencv_chinese_test.png: OpenCV绘制的中文（可能显示为问号）")
    print("- pil_chinese_test.png: PIL绘制的中文（正常显示）")
    print("- annotated_with_issue.png: 当前标注方法（中文可能显示为问号）")
    print("- annotated_fixed.png: 修复后的标注方法（中文正常显示）")


def main():
    """主测试函数"""
    print("开始中文字符显示问题测试...")
    
    # 创建测试目录
    os.makedirs("temp", exist_ok=True)
    
    # 运行测试
    test_fixed_chinese_display()
    
    print("\n测试完成！请查看 temp/chinese_display_test/ 目录中的图像文件")


if __name__ == "__main__":
    main()
