"""
关键问题修复验证测试
验证运行时错误修复、UI功能增强和性能优化
"""

import cv2
import numpy as np
import os
import time
import threading
from pathlib import Path

from core.ocr import OCREngine, SearchMode
from core.action import ActionExecutor
from utils.visualization import ImageAnnotator
from utils.performance_monitor import performance_monitor
from utils.code_generator import CodeGenerator
from utils.logger import get_logger

logger = get_logger("test_critical_fixes")


def test_runtime_error_fixes():
    """测试运行时错误修复"""
    logger.info("=== 测试运行时错误修复 ===")
    
    test_results = {}
    
    # 测试1: 状态栏变量修复
    try:
        from gui.main_window import MainWindow
        import tkinter as tk

        # 创建主窗口实例（MainWindow会创建自己的root）
        main_window = MainWindow()
        main_window.root.withdraw()  # 隐藏窗口

        # 检查status_var是否存在
        if hasattr(main_window, 'status_var'):
            main_window.status_var.set("测试状态")
            test_results['status_var_fix'] = True
            logger.info("✅ status_var属性修复成功")
        else:
            test_results['status_var_fix'] = False
            logger.error("❌ status_var属性仍然缺失")

        main_window.root.destroy()
        
    except Exception as e:
        test_results['status_var_fix'] = False
        logger.error(f"❌ 状态栏测试失败: {e}")
    
    # 测试2: OpenCV坐标类型修复
    try:
        # 创建测试图像
        test_image = np.ones((300, 400, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "Test Text", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 创建可视化器
        annotator = ImageAnnotator()
        
        # 创建模拟OCR结果（包含浮点坐标）
        from core.ocr import OCRResult
        bbox = [[50.5, 80.7], [150.3, 80.7], [150.3, 110.9], [50.5, 110.9]]
        ocr_result = OCRResult("Test Text", 0.95, bbox)
        
        # 测试标注功能
        annotated_image = annotator.annotate_ocr_results(test_image, [ocr_result])
        
        if annotated_image is not None:
            test_results['opencv_coordinate_fix'] = True
            logger.info("✅ OpenCV坐标类型修复成功")
        else:
            test_results['opencv_coordinate_fix'] = False
            logger.error("❌ OpenCV坐标类型修复失败")
        
    except Exception as e:
        test_results['opencv_coordinate_fix'] = False
        logger.error(f"❌ OpenCV坐标测试失败: {e}")
    
    return test_results


def test_ui_enhancements():
    """测试UI功能增强"""
    logger.info("=== 测试UI功能增强 ===")
    
    test_results = {}
    
    # 测试1: 区域选择器
    try:
        from utils.region_selector import RegionSelector
        
        # 创建区域选择器实例
        selector = RegionSelector()
        
        if hasattr(selector, 'start_selection'):
            test_results['region_selector'] = True
            logger.info("✅ 区域选择器创建成功")
        else:
            test_results['region_selector'] = False
            logger.error("❌ 区域选择器缺少必要方法")
        
    except Exception as e:
        test_results['region_selector'] = False
        logger.error(f"❌ 区域选择器测试失败: {e}")
    
    # 测试2: 代码生成器
    try:
        code_generator = CodeGenerator()
        
        # 测试搜索配置
        search_configs = [
            {'search_term': 'test', 'mode': 'exact', 'similarity_threshold': 0.8},
            {'search_term': 'button', 'mode': 'contains', 'similarity_threshold': 0.7}
        ]
        
        # 生成独立脚本
        script_path = code_generator.generate_standalone_script(
            search_configs=search_configs,
            script_name="test_generated_script"
        )
        
        if os.path.exists(script_path):
            test_results['code_generator'] = True
            logger.info(f"✅ 代码生成器测试成功: {script_path}")
            
            # 清理测试文件
            try:
                os.remove(script_path)
            except:
                pass
        else:
            test_results['code_generator'] = False
            logger.error("❌ 代码生成器未生成文件")
        
    except Exception as e:
        test_results['code_generator'] = False
        logger.error(f"❌ 代码生成器测试失败: {e}")
    
    # 测试3: 图像导入功能（模拟）
    try:
        # 创建测试图像文件
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "Import Test", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        test_image_path = "temp/test_import_image.png"
        os.makedirs("temp", exist_ok=True)
        cv2.imwrite(test_image_path, test_image)
        
        # 测试图像加载
        loaded_image = cv2.imread(test_image_path)
        
        if loaded_image is not None:
            test_results['image_import'] = True
            logger.info("✅ 图像导入功能测试成功")
        else:
            test_results['image_import'] = False
            logger.error("❌ 图像导入功能测试失败")
        
        # 清理测试文件
        try:
            os.remove(test_image_path)
        except:
            pass
        
    except Exception as e:
        test_results['image_import'] = False
        logger.error(f"❌ 图像导入测试失败: {e}")
    
    return test_results


def test_performance_optimizations():
    """测试性能优化"""
    logger.info("=== 测试性能优化 ===")
    
    test_results = {}
    
    # 测试1: OCR缓存功能
    try:
        ocr_engine = OCREngine()
        
        # 创建测试图像
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "Cache Test", (100, 200), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        
        # 第一次识别（无缓存）
        start_time = time.time()
        results1 = ocr_engine.recognize_text(test_image, use_cache=True)
        first_time = time.time() - start_time
        
        # 第二次识别（使用缓存）
        start_time = time.time()
        results2 = ocr_engine.recognize_text(test_image, use_cache=True)
        second_time = time.time() - start_time
        
        # 检查缓存效果
        if second_time < first_time * 0.5:  # 缓存应该显著提高速度
            test_results['ocr_cache'] = True
            logger.info(f"✅ OCR缓存功能正常 (第一次: {first_time:.3f}s, 第二次: {second_time:.3f}s)")
        else:
            test_results['ocr_cache'] = False
            logger.warning(f"⚠️ OCR缓存效果不明显 (第一次: {first_time:.3f}s, 第二次: {second_time:.3f}s)")
        
        # 测试缓存信息
        cache_info = ocr_engine.get_cache_info()
        logger.info(f"缓存信息: {cache_info}")
        
    except Exception as e:
        test_results['ocr_cache'] = False
        logger.error(f"❌ OCR缓存测试失败: {e}")
    
    # 测试2: 性能监控
    try:
        # 清除之前的监控数据
        performance_monitor.clear_history()
        
        # 执行一些监控操作
        operation_id = performance_monitor.start_operation("test_operation", {"test": True})
        time.sleep(0.1)  # 模拟操作
        performance_monitor.end_operation(operation_id, {"result": "success"})
        
        # 获取性能摘要
        summary = performance_monitor.get_performance_summary()
        
        if summary.get('total_operations', 0) > 0:
            test_results['performance_monitor'] = True
            logger.info("✅ 性能监控功能正常")
            logger.info(f"性能摘要: {summary}")
        else:
            test_results['performance_monitor'] = False
            logger.error("❌ 性能监控未记录操作")
        
    except Exception as e:
        test_results['performance_monitor'] = False
        logger.error(f"❌ 性能监控测试失败: {e}")
    
    # 测试3: 图像预处理优化
    try:
        ocr_engine = OCREngine()
        
        # 创建大尺寸测试图像
        large_image = np.ones((2000, 3000, 3), dtype=np.uint8) * 255
        cv2.putText(large_image, "Large Image Test", (500, 1000), cv2.FONT_HERSHEY_SIMPLEX, 5, (0, 0, 0), 10)
        
        # 测试预处理
        processed_image = ocr_engine._preprocess_image(large_image)
        
        # 检查是否进行了缩放
        if processed_image.shape[0] < large_image.shape[0] or processed_image.shape[1] < large_image.shape[1]:
            test_results['image_preprocessing'] = True
            logger.info(f"✅ 图像预处理优化正常 (原始: {large_image.shape}, 处理后: {processed_image.shape})")
        else:
            test_results['image_preprocessing'] = False
            logger.warning("⚠️ 图像预处理未进行缩放优化")
        
    except Exception as e:
        test_results['image_preprocessing'] = False
        logger.error(f"❌ 图像预处理测试失败: {e}")
    
    return test_results


def test_memory_management():
    """测试内存管理"""
    logger.info("=== 测试内存管理 ===")
    
    test_results = {}
    
    try:
        # 获取初始内存使用
        initial_memory = performance_monitor.get_system_info().get('process_memory', 0)
        
        # 创建大量OCR操作
        ocr_engine = OCREngine()
        
        for i in range(10):
            # 创建测试图像
            test_image = np.random.randint(0, 255, (500, 800, 3), dtype=np.uint8)
            cv2.putText(test_image, f"Test {i}", (100, 250), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
            
            # 执行OCR
            results = ocr_engine.recognize_text(test_image)
        
        # 执行内存优化
        optimization_result = performance_monitor.optimize_memory()
        
        # 获取优化后内存使用
        final_memory = performance_monitor.get_system_info().get('process_memory', 0)
        
        logger.info(f"内存使用: 初始 {initial_memory:.2f}MB, 最终 {final_memory:.2f}MB")
        logger.info(f"内存优化结果: {optimization_result}")
        
        test_results['memory_management'] = True
        logger.info("✅ 内存管理测试完成")
        
    except Exception as e:
        test_results['memory_management'] = False
        logger.error(f"❌ 内存管理测试失败: {e}")
    
    return test_results


def main():
    """主测试函数"""
    logger.info("开始关键问题修复验证测试...")
    
    # 确保必要目录存在
    os.makedirs("temp", exist_ok=True)
    os.makedirs("generated_scripts", exist_ok=True)
    
    all_results = {}
    
    # 执行所有测试
    all_results['runtime_fixes'] = test_runtime_error_fixes()
    all_results['ui_enhancements'] = test_ui_enhancements()
    all_results['performance_optimizations'] = test_performance_optimizations()
    all_results['memory_management'] = test_memory_management()
    
    # 统计结果
    total_tests = 0
    passed_tests = 0
    
    logger.info("=" * 60)
    logger.info("关键问题修复验证测试总结:")
    
    for category, results in all_results.items():
        logger.info(f"\n{category.upper()}:")
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
            total_tests += 1
            if result:
                passed_tests += 1
    
    logger.info(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有关键问题修复验证测试通过！")
        logger.info("🎉 所有关键问题修复验证测试通过！")
    else:
        print(f"⚠️ {total_tests - passed_tests} 个测试失败，请检查日志")
        logger.warning(f"⚠️ {total_tests - passed_tests} 个测试失败")
    
    # 导出性能数据
    try:
        performance_monitor.export_metrics("temp/performance_test_results.json")
        logger.info("性能测试数据已导出到 temp/performance_test_results.json")
    except Exception as e:
        logger.warning(f"导出性能数据失败: {e}")


if __name__ == "__main__":
    main()
