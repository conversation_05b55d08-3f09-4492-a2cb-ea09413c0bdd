"""
完整功能测试脚本
验证所有OCR搜索和自动化功能
"""

import cv2
import numpy as np
import os
import time
from core.ocr import OCREngine, SearchMode
from core.action import ActionExecutor
from utils.visualization import ImageAnnotator
from utils.logger import get_logger

logger = get_logger("test_complete")


def create_comprehensive_test_image():
    """创建包含多种文本的综合测试图像"""
    # 创建更大的测试图像
    image = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(image, "OCR Search Test Image", (200, 50), font, 1.2, (0, 0, 0), 2)
    
    # 添加各种文本内容
    texts = [
        ("Login Button", (50, 120), 1.0, (0, 0, 255)),      # 红色
        ("Username: admin", (50, 180), 0.8, (0, 128, 0)),   # 绿色
        ("Password: ****", (50, 240), 0.8, (0, 128, 0)),    # 绿色
        ("Submit Form", (400, 120), 1.0, (255, 0, 0)),      # 蓝色
        ("Cancel Operation", (400, 180), 0.9, (128, 128, 128)), # 灰色
        ("User ID: 12345", (50, 300), 0.7, (0, 0, 0)),      # 黑色
        ("Email: <EMAIL>", (50, 360), 0.7, (0, 0, 0)), # 黑色
        ("Status: Active", (400, 240), 0.8, (0, 200, 0)),   # 深绿色
        ("Version 1.0.0", (400, 300), 0.6, (100, 100, 100)), # 深灰色
        ("Help & Support", (400, 360), 0.8, (0, 0, 200)),   # 深蓝色
        ("Settings Menu", (50, 420), 0.9, (128, 0, 128)),   # 紫色
        ("Exit Application", (400, 420), 0.9, (200, 0, 0)), # 深红色
        ("Search: keyword", (50, 480), 0.8, (0, 100, 200)), # 棕色
        ("Results: 25 found", (400, 480), 0.8, (0, 150, 150)), # 青绿色
        ("Page 1 of 5", (300, 540), 0.7, (150, 150, 0)),    # 橄榄色
    ]
    
    for text, pos, scale, color in texts:
        cv2.putText(image, text, pos, font, scale, color, 2)
    
    # 添加一些装饰性元素
    cv2.rectangle(image, (30, 100), (770, 520), (200, 200, 200), 2)
    cv2.line(image, (400, 100), (400, 520), (200, 200, 200), 1)
    
    return image


def test_all_search_modes():
    """测试所有搜索模式"""
    logger.info("=== 测试所有搜索模式 ===")
    
    ocr_engine = OCREngine()
    test_image = create_comprehensive_test_image()
    
    # 保存测试图像
    cv2.imwrite("temp/comprehensive_test.png", test_image)
    logger.info("综合测试图像已保存")
    
    # 测试用例
    test_cases = [
        # (搜索词, 模式, 预期结果数量, 描述)
        ("Login Button", SearchMode.EXACT, 1, "精确匹配测试"),
        ("login", SearchMode.CONTAINS, 1, "包含匹配测试（忽略大小写）"),
        ("Logn", SearchMode.FUZZY, 1, "模糊匹配测试（拼写错误）"),
        (r"User.*\d+", SearchMode.REGEX, 1, "正则表达式测试"),
        ("Button", SearchMode.SIMILARITY, 2, "相似度匹配测试"),
        ("admin", SearchMode.CONTAINS, 1, "用户名搜索"),
        ("test@", SearchMode.CONTAINS, 1, "邮箱搜索"),
        ("Version", SearchMode.EXACT, 1, "版本信息搜索"),
    ]
    
    all_results = {}
    
    for search_term, mode, expected, description in test_cases:
        logger.info(f"测试: {description}")
        logger.info(f"  搜索词: '{search_term}', 模式: {mode.value}")
        
        try:
            results = ocr_engine.advanced_search(
                image=test_image,
                search_terms=search_term,
                mode=mode,
                similarity_threshold=0.7
            )
            
            all_results[search_term] = results
            
            logger.info(f"  找到 {len(results)} 个结果 (预期: {expected})")
            
            for i, result in enumerate(results):
                logger.info(f"    结果 {i+1}: '{result.match_text}' (分数: {result.match_score:.3f})")
            
            # 验证结果
            if len(results) >= expected:
                logger.info(f"  ✅ 测试通过")
            else:
                logger.warning(f"  ⚠️ 结果数量少于预期")
                
        except Exception as e:
            logger.error(f"  ❌ 测试失败: {e}")
    
    return all_results


def test_batch_search():
    """测试批量搜索功能"""
    logger.info("=== 测试批量搜索功能 ===")
    
    ocr_engine = OCREngine()
    test_image = create_comprehensive_test_image()
    
    # 批量搜索配置
    search_configs = [
        {'search_term': 'Button', 'mode': 'contains'},
        {'search_term': 'User', 'mode': 'contains'},
        {'search_term': 'Status', 'mode': 'exact'},
        {'search_term': r'\d+', 'mode': 'regex'},  # 搜索数字
        {'search_term': 'Menu', 'mode': 'contains'},
    ]
    
    try:
        batch_results = ocr_engine.batch_search(test_image, search_configs)
        
        logger.info(f"批量搜索完成，共 {len(batch_results)} 组结果")
        
        total_matches = 0
        for search_term, results in batch_results.items():
            logger.info(f"  '{search_term}': {len(results)} 个匹配")
            total_matches += len(results)
        
        logger.info(f"总匹配数: {total_matches}")
        
        return batch_results
        
    except Exception as e:
        logger.error(f"批量搜索测试失败: {e}")
        return {}


def test_visualization():
    """测试可视化功能"""
    logger.info("=== 测试可视化功能 ===")
    
    try:
        ocr_engine = OCREngine()
        annotator = ImageAnnotator()
        test_image = create_comprehensive_test_image()
        
        # 执行搜索
        search_results = ocr_engine.advanced_search(
            image=test_image,
            search_terms=["Button", "User", "Status"],
            mode=SearchMode.CONTAINS
        )
        
        if search_results:
            # 创建标注图像
            annotated_image = annotator.annotate_search_results(
                test_image, search_results
            )
            cv2.imwrite("temp/annotated_comprehensive.png", annotated_image)
            logger.info("标注图像已保存到 temp/annotated_comprehensive.png")
            
            # 创建覆盖层图像
            overlay_image = annotator.create_result_overlay(
                test_image, search_results, overlay_alpha=0.3
            )
            cv2.imwrite("temp/overlay_comprehensive.png", overlay_image)
            logger.info("覆盖层图像已保存到 temp/overlay_comprehensive.png")
            
            # 创建结果摘要
            summary_image = annotator.create_result_summary_image(search_results)
            cv2.imwrite("temp/summary_comprehensive.png", summary_image)
            logger.info("结果摘要已保存到 temp/summary_comprehensive.png")
            
            logger.info("✅ 可视化测试完成")
            return True
        else:
            logger.warning("⚠️ 没有搜索结果可用于可视化测试")
            return False
            
    except Exception as e:
        logger.error(f"❌ 可视化测试失败: {e}")
        return False


def test_action_queue():
    """测试动作队列功能"""
    logger.info("=== 测试动作队列功能 ===")
    
    try:
        action_executor = ActionExecutor()
        ocr_engine = OCREngine()
        test_image = create_comprehensive_test_image()
        
        # 获取一些搜索结果
        search_results = ocr_engine.advanced_search(
            image=test_image,
            search_terms="Button",
            mode=SearchMode.CONTAINS
        )
        
        if search_results:
            # 创建动作队列
            action_queue = action_executor.create_action_queue()
            
            # 添加动作（测试模式，不实际执行点击）
            for i, result in enumerate(search_results[:3]):  # 只测试前3个
                action_queue.add_click_action(result.ocr_result)
                action_queue.add_wait_action(0.1)
            
            logger.info(f"动作队列创建完成，包含 {len(action_queue.actions)} 个动作")
            
            # 获取队列摘要
            summary = action_queue.get_summary()
            logger.info(f"队列摘要: {summary}")
            
            logger.info("✅ 动作队列测试完成")
            return True
        else:
            logger.warning("⚠️ 没有搜索结果可用于动作队列测试")
            return False
            
    except Exception as e:
        logger.error(f"❌ 动作队列测试失败: {e}")
        return False


def test_search_statistics():
    """测试搜索统计功能"""
    logger.info("=== 测试搜索统计功能 ===")
    
    try:
        ocr_engine = OCREngine()
        test_image = create_comprehensive_test_image()
        
        # 执行多种搜索
        all_results = []
        
        search_terms = ["Button", "User", "Status", "Version", "Menu"]
        for term in search_terms:
            results = ocr_engine.advanced_search(
                image=test_image,
                search_terms=term,
                mode=SearchMode.CONTAINS
            )
            all_results.extend(results)
        
        if all_results:
            # 获取统计信息
            stats = ocr_engine.get_search_statistics(all_results)
            
            logger.info("搜索统计信息:")
            logger.info(f"  总结果数: {stats.get('total_results', 0)}")
            logger.info(f"  平均置信度: {stats.get('average_confidence', 0):.3f}")
            logger.info(f"  平均匹配分数: {stats.get('average_match_score', 0):.3f}")
            logger.info(f"  模式分布: {stats.get('mode_distribution', {})}")
            logger.info(f"  置信度分布: {stats.get('confidence_distribution', {})}")
            
            best_match = stats.get('best_match')
            if best_match:
                logger.info(f"  最佳匹配: {best_match}")
            
            logger.info("✅ 搜索统计测试完成")
            return True
        else:
            logger.warning("⚠️ 没有搜索结果可用于统计测试")
            return False
            
    except Exception as e:
        logger.error(f"❌ 搜索统计测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始完整功能测试...")
    
    # 确保temp目录存在
    os.makedirs("temp", exist_ok=True)
    
    test_results = {}
    
    # 执行所有测试
    test_results['search_modes'] = test_all_search_modes()
    test_results['batch_search'] = test_batch_search()
    test_results['visualization'] = test_visualization()
    test_results['action_queue'] = test_action_queue()
    test_results['statistics'] = test_search_statistics()
    
    # 统计测试结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    logger.info("=" * 50)
    logger.info("完整功能测试总结:")
    logger.info(f"通过测试: {passed_tests}/{total_tests}")
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("🎉 所有功能测试通过！")
        logger.info("🎉 所有功能测试通过！")
    else:
        print(f"⚠️ {total_tests - passed_tests} 个测试失败，请检查日志")
        logger.warning(f"⚠️ {total_tests - passed_tests} 个测试失败")
    
    logger.info("测试完成，生成的文件:")
    logger.info("  - temp/comprehensive_test.png (测试图像)")
    logger.info("  - temp/annotated_comprehensive.png (标注图像)")
    logger.info("  - temp/overlay_comprehensive.png (覆盖层图像)")
    logger.info("  - temp/summary_comprehensive.png (结果摘要)")


if __name__ == "__main__":
    main()
