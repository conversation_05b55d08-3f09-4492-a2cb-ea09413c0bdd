"""
动作执行层
集成PyAutoGUI，提供鼠标点击、键盘输入和等待机制
"""

import pyautogui
import time
import random
from typing import Tuple, Optional, List, Union, Any, Dict
from pathlib import Path
import cv2
import numpy as np

from utils.config import config
from utils.logger import get_logger
from .screen import ScreenCapture
from .match import TemplateMatch, MatchResult
from .ocr import OCRResult, SearchResult

logger = get_logger("action")


class ActionExecutor:
    """动作执行器"""
    
    def __init__(self):
        """初始化动作执行器"""
        self.config = config.get_section('action')
        self.screen_capture = ScreenCapture()
        self.template_match = TemplateMatch()
        
        # 设置pyautogui参数
        pyautogui.FAILSAFE = self.config.get('fail_safe', True)
        pyautogui.PAUSE = self.config.get('pause', 0.1)
        
        logger.info("ActionExecutor初始化完成")
    
    def click(self, x: int, y: int, button: str = 'left', 
              clicks: int = 1, interval: float = 0.0) -> bool:
        """
        点击指定坐标
        
        Args:
            x: X坐标
            y: Y坐标
            button: 鼠标按钮 ('left', 'right', 'middle')
            clicks: 点击次数
            interval: 点击间隔
            
        Returns:
            是否成功
        """
        try:
            # 添加随机偏移，模拟人类行为
            offset_x = random.randint(-2, 2)
            offset_y = random.randint(-2, 2)
            actual_x = x + offset_x
            actual_y = y + offset_y
            
            # 执行点击
            pyautogui.click(actual_x, actual_y, clicks=clicks, 
                           interval=interval, button=button)
            
            # 等待
            delay = self.config.get('click_delay', 0.1)
            time.sleep(delay)
            
            logger.info(f"点击坐标: ({actual_x}, {actual_y}), 按钮: {button}")
            return True
            
        except Exception as e:
            logger.error(f"点击失败: {e}")
            return False
    
    def double_click(self, x: int, y: int) -> bool:
        """双击指定坐标"""
        return self.click(x, y, clicks=2, interval=0.1)
    
    def right_click(self, x: int, y: int) -> bool:
        """右键点击指定坐标"""
        return self.click(x, y, button='right')
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int,
             duration: float = 1.0) -> bool:
        """
        拖拽操作
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 拖拽持续时间
            
        Returns:
            是否成功
        """
        try:
            pyautogui.drag(end_x - start_x, end_y - start_y, 
                          duration=duration, button='left')
            
            logger.info(f"拖拽: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
            return True
            
        except Exception as e:
            logger.error(f"拖拽失败: {e}")
            return False
    
    def scroll(self, x: int, y: int, clicks: int) -> bool:
        """
        滚动操作
        
        Args:
            x: X坐标
            y: Y坐标
            clicks: 滚动次数（正数向上，负数向下）
            
        Returns:
            是否成功
        """
        try:
            pyautogui.scroll(clicks, x=x, y=y)
            
            logger.info(f"滚动: ({x}, {y}), 次数: {clicks}")
            return True
            
        except Exception as e:
            logger.error(f"滚动失败: {e}")
            return False
    
    def type_text(self, text: str, interval: Optional[float] = None) -> bool:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            interval: 字符间隔时间
            
        Returns:
            是否成功
        """
        try:
            interval = interval or self.config.get('type_delay', 0.05)
            
            # 使用typewrite而不是write，支持中文
            pyautogui.typewrite(text, interval=interval)
            
            logger.info(f"输入文本: '{text}'")
            return True
            
        except Exception as e:
            logger.error(f"输入文本失败: {e}")
            return False
    
    def press_key(self, key: str, presses: int = 1, interval: float = 0.0) -> bool:
        """
        按键操作
        
        Args:
            key: 按键名称
            presses: 按键次数
            interval: 按键间隔
            
        Returns:
            是否成功
        """
        try:
            pyautogui.press(key, presses=presses, interval=interval)
            
            logger.info(f"按键: {key}, 次数: {presses}")
            return True
            
        except Exception as e:
            logger.error(f"按键失败: {e}")
            return False
    
    def key_combination(self, *keys) -> bool:
        """
        组合键操作
        
        Args:
            *keys: 按键组合
            
        Returns:
            是否成功
        """
        try:
            pyautogui.hotkey(*keys)
            
            logger.info(f"组合键: {'+'.join(keys)}")
            return True
            
        except Exception as e:
            logger.error(f"组合键失败: {e}")
            return False
    
    def wait_for_image(self, template_path: str, timeout: float = 10.0,
                      confidence: float = 0.9, region: Optional[Tuple] = None) -> Optional[MatchResult]:
        """
        等待图像出现
        
        Args:
            template_path: 模板图像路径
            timeout: 超时时间（秒）
            confidence: 置信度阈值
            region: 搜索区域
            
        Returns:
            匹配结果或None
        """
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # 截图
                screenshot = self.screen_capture.capture_screen(region)
                
                # 查找模板
                matches = self.template_match.find_template(
                    screenshot, template_path, threshold=confidence
                )
                
                if matches:
                    logger.info(f"找到图像: {template_path}, 耗时: {time.time() - start_time:.2f}s")
                    return matches[0]  # 返回最佳匹配
                
                # 等待间隔
                time.sleep(self.config.get('screenshot_delay', 0.2))
            
            logger.warning(f"等待图像超时: {template_path}")
            return None
            
        except Exception as e:
            logger.error(f"等待图像失败: {e}")
            return None
    
    def click_image(self, template_path: str, timeout: float = 10.0,
                   confidence: float = 0.9, region: Optional[Tuple] = None) -> bool:
        """
        点击图像
        
        Args:
            template_path: 模板图像路径
            timeout: 超时时间
            confidence: 置信度阈值
            region: 搜索区域
            
        Returns:
            是否成功
        """
        try:
            # 等待图像出现
            match_result = self.wait_for_image(template_path, timeout, confidence, region)
            
            if match_result:
                # 点击中心点
                center_x, center_y = match_result.center
                return self.click(center_x, center_y)
            
            logger.warning(f"未找到图像，无法点击: {template_path}")
            return False
            
        except Exception as e:
            logger.error(f"点击图像失败: {e}")
            return False

    def wait_for_text(self, target_text: str, timeout: float = 10.0,
                     region: Optional[Tuple] = None, fuzzy_match: bool = False) -> bool:
        """
        等待文本出现

        Args:
            target_text: 目标文本
            timeout: 超时时间
            region: 搜索区域
            fuzzy_match: 是否模糊匹配

        Returns:
            是否找到文本
        """
        try:
            from .ocr import OCREngine
            ocr_engine = OCREngine()

            start_time = time.time()

            while time.time() - start_time < timeout:
                # 截图
                screenshot = self.screen_capture.capture_screen(region)

                # OCR识别
                results = ocr_engine.find_text(screenshot, target_text, fuzzy_match=fuzzy_match)

                if results:
                    logger.info(f"找到文本: '{target_text}', 耗时: {time.time() - start_time:.2f}s")
                    return True

                # 等待间隔
                time.sleep(self.config.get('screenshot_delay', 0.2))

            logger.warning(f"等待文本超时: '{target_text}'")
            return False

        except Exception as e:
            logger.error(f"等待文本失败: {e}")
            return False

    def click_text(self, target_text: str, timeout: float = 10.0,
                  region: Optional[Tuple] = None, fuzzy_match: bool = False) -> bool:
        """
        点击文本

        Args:
            target_text: 目标文本
            timeout: 超时时间
            region: 搜索区域
            fuzzy_match: 是否模糊匹配

        Returns:
            是否成功
        """
        try:
            from .ocr import OCREngine
            ocr_engine = OCREngine()

            start_time = time.time()

            while time.time() - start_time < timeout:
                # 截图
                screenshot = self.screen_capture.capture_screen(region)

                # OCR识别
                results = ocr_engine.find_text(screenshot, target_text, fuzzy_match=fuzzy_match)

                if results:
                    # 点击第一个匹配的文本中心
                    center_x, center_y = results[0].center
                    logger.info(f"点击文本: '{target_text}' at ({center_x}, {center_y})")
                    return self.click(center_x, center_y)

                # 等待间隔
                time.sleep(self.config.get('screenshot_delay', 0.2))

            logger.warning(f"未找到文本，无法点击: '{target_text}'")
            return False

        except Exception as e:
            logger.error(f"点击文本失败: {e}")
            return False

    def move_mouse(self, x: int, y: int, duration: float = 0.5) -> bool:
        """
        移动鼠标

        Args:
            x: 目标X坐标
            y: 目标Y坐标
            duration: 移动持续时间

        Returns:
            是否成功
        """
        try:
            pyautogui.moveTo(x, y, duration=duration)

            logger.debug(f"鼠标移动到: ({x}, {y})")
            return True

        except Exception as e:
            logger.error(f"鼠标移动失败: {e}")
            return False

    def get_mouse_position(self) -> Tuple[int, int]:
        """获取当前鼠标位置"""
        try:
            pos = pyautogui.position()
            return pos.x, pos.y
        except Exception as e:
            logger.error(f"获取鼠标位置失败: {e}")
            return (0, 0)

    def wait_and_click(self, x: int, y: int, wait_time: float = 1.0) -> bool:
        """等待后点击"""
        time.sleep(wait_time)
        return self.click(x, y)

    def safe_click(self, x: int, y: int, verify_template: Optional[str] = None,
                  max_attempts: int = 3) -> bool:
        """
        安全点击（带验证）

        Args:
            x: X坐标
            y: Y坐标
            verify_template: 验证模板路径
            max_attempts: 最大尝试次数

        Returns:
            是否成功
        """
        for attempt in range(max_attempts):
            try:
                # 执行点击
                if not self.click(x, y):
                    continue

                # 如果有验证模板，检查点击效果
                if verify_template:
                    time.sleep(0.5)  # 等待界面响应
                    screenshot = self.screen_capture.capture_screen()
                    matches = self.template_match.find_template(screenshot, verify_template)

                    if matches:
                        logger.info(f"安全点击成功，验证通过: {verify_template}")
                        return True
                    else:
                        logger.warning(f"点击验证失败，第{attempt+1}次重试")
                        continue
                else:
                    return True

            except Exception as e:
                logger.warning(f"安全点击失败，第{attempt+1}次重试: {e}")
                time.sleep(0.5)

        logger.error(f"安全点击失败，已重试{max_attempts}次")
        return False

    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            # 清理截图临时文件
            self.screen_capture.cleanup_temp_screenshots()
            logger.info("临时文件清理完成")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")

    # ==================== OCR相关操作方法 ====================

    def click_ocr_result(self, ocr_result: OCRResult,
                        button: str = 'left', clicks: int = 1,
                        offset: Tuple[int, int] = (0, 0),
                        verify_before_click: bool = True) -> bool:
        """
        点击OCR识别结果

        Args:
            ocr_result: OCR识别结果
            button: 鼠标按钮
            clicks: 点击次数
            offset: 坐标偏移量 (x_offset, y_offset)
            verify_before_click: 点击前是否验证坐标

        Returns:
            是否成功
        """
        try:
            # 获取点击坐标
            center_x, center_y = ocr_result.center
            click_x = center_x + offset[0]
            click_y = center_y + offset[1]

            # 验证坐标
            if verify_before_click:
                if not self._verify_coordinates(click_x, click_y):
                    logger.error(f"坐标验证失败: ({click_x}, {click_y})")
                    return False

            # 执行点击
            success = self.click(click_x, click_y, button, clicks)

            if success:
                logger.info(f"成功点击OCR结果: '{ocr_result.text}' at ({click_x}, {click_y})")
            else:
                logger.error(f"点击OCR结果失败: '{ocr_result.text}'")

            return success

        except Exception as e:
            logger.error(f"点击OCR结果失败: {e}")
            return False

    def click_search_result(self, search_result: SearchResult,
                           button: str = 'left', clicks: int = 1,
                           offset: Tuple[int, int] = (0, 0)) -> bool:
        """
        点击搜索结果

        Args:
            search_result: 搜索结果
            button: 鼠标按钮
            clicks: 点击次数
            offset: 坐标偏移量

        Returns:
            是否成功
        """
        try:
            success = self.click_ocr_result(
                search_result.ocr_result,
                button, clicks, offset
            )

            if success:
                logger.info(f"成功点击搜索结果: '{search_result.search_term}' -> '{search_result.match_text}'")

            return success

        except Exception as e:
            logger.error(f"点击搜索结果失败: {e}")
            return False

    def batch_click_search_results(self, search_results: List[SearchResult],
                                  click_delay: float = 1.0,
                                  button: str = 'left',
                                  max_clicks: Optional[int] = None) -> Dict[str, bool]:
        """
        批量点击搜索结果

        Args:
            search_results: 搜索结果列表
            click_delay: 点击间隔时间
            button: 鼠标按钮
            max_clicks: 最大点击数量

        Returns:
            点击结果字典 {result_id: success}
        """
        try:
            results = {}
            click_count = 0

            for i, search_result in enumerate(search_results):
                if max_clicks and click_count >= max_clicks:
                    break

                result_id = f"result_{i}_{search_result.search_term}"

                # 执行点击
                success = self.click_search_result(search_result, button)
                results[result_id] = success

                if success:
                    click_count += 1
                    logger.info(f"批量点击 {click_count}: {search_result.match_text}")

                # 等待间隔
                if i < len(search_results) - 1:  # 最后一个不需要等待
                    time.sleep(click_delay)

            logger.info(f"批量点击完成，成功点击{click_count}个结果")
            return results

        except Exception as e:
            logger.error(f"批量点击搜索结果失败: {e}")
            return {}

    def hover_ocr_result(self, ocr_result: OCRResult,
                        duration: float = 1.0,
                        offset: Tuple[int, int] = (0, 0)) -> bool:
        """
        悬停在OCR结果上

        Args:
            ocr_result: OCR识别结果
            duration: 悬停时间
            offset: 坐标偏移量

        Returns:
            是否成功
        """
        try:
            center_x, center_y = ocr_result.center
            hover_x = center_x + offset[0]
            hover_y = center_y + offset[1]

            # 验证坐标
            if not self._verify_coordinates(hover_x, hover_y):
                logger.error(f"悬停坐标验证失败: ({hover_x}, {hover_y})")
                return False

            # 移动到目标位置
            pyautogui.moveTo(hover_x, hover_y, duration=0.5)

            # 悬停
            time.sleep(duration)

            logger.info(f"悬停在OCR结果: '{ocr_result.text}' at ({hover_x}, {hover_y})")
            return True

        except Exception as e:
            logger.error(f"悬停在OCR结果失败: {e}")
            return False

    def drag_between_ocr_results(self, start_result: OCRResult, end_result: OCRResult,
                                duration: float = 1.0, button: str = 'left') -> bool:
        """
        在两个OCR结果之间拖拽

        Args:
            start_result: 起始OCR结果
            end_result: 结束OCR结果
            duration: 拖拽持续时间
            button: 鼠标按钮

        Returns:
            是否成功
        """
        try:
            start_x, start_y = start_result.center
            end_x, end_y = end_result.center

            # 验证坐标
            if not (self._verify_coordinates(start_x, start_y) and
                   self._verify_coordinates(end_x, end_y)):
                logger.error("拖拽坐标验证失败")
                return False

            # 执行拖拽
            pyautogui.drag(end_x - start_x, end_y - start_y,
                          duration=duration, button=button)

            logger.info(f"拖拽完成: '{start_result.text}' -> '{end_result.text}'")
            return True

        except Exception as e:
            logger.error(f"拖拽操作失败: {e}")
            return False

    def type_near_ocr_result(self, ocr_result: OCRResult, text: str,
                           offset: Tuple[int, int] = (0, 30),
                           click_first: bool = True) -> bool:
        """
        在OCR结果附近输入文本

        Args:
            ocr_result: OCR识别结果
            text: 要输入的文本
            offset: 输入位置偏移量
            click_first: 是否先点击定位

        Returns:
            是否成功
        """
        try:
            if click_first:
                # 先点击定位
                if not self.click_ocr_result(ocr_result, offset=offset):
                    logger.error("点击定位失败")
                    return False

                # 等待一下
                time.sleep(0.2)

            # 输入文本
            success = self.type_text(text)

            if success:
                logger.info(f"在OCR结果'{ocr_result.text}'附近输入文本: '{text}'")

            return success

        except Exception as e:
            logger.error(f"在OCR结果附近输入文本失败: {e}")
            return False

    def get_ocr_result_region(self, ocr_result: OCRResult,
                             padding: int = 10) -> Tuple[int, int, int, int]:
        """
        获取OCR结果的区域坐标（带边距）

        Args:
            ocr_result: OCR识别结果
            padding: 边距

        Returns:
            区域坐标 (x, y, width, height)
        """
        try:
            x, y, width, height = ocr_result.rect

            # 添加边距
            padded_x = max(0, x - padding)
            padded_y = max(0, y - padding)
            padded_width = width + 2 * padding
            padded_height = height + 2 * padding

            return padded_x, padded_y, padded_width, padded_height

        except Exception as e:
            logger.error(f"获取OCR结果区域失败: {e}")
            return 0, 0, 0, 0

    def _verify_coordinates(self, x: int, y: int) -> bool:
        """
        验证坐标是否在屏幕范围内

        Args:
            x: X坐标
            y: Y坐标

        Returns:
            是否有效
        """
        try:
            screen_width, screen_height = pyautogui.size()

            if 0 <= x < screen_width and 0 <= y < screen_height:
                return True
            else:
                logger.warning(f"坐标超出屏幕范围: ({x}, {y}), 屏幕尺寸: ({screen_width}, {screen_height})")
                return False

        except Exception as e:
            logger.error(f"坐标验证失败: {e}")
            return False

    def create_action_queue(self) -> 'ActionQueue':
        """
        创建动作队列

        Returns:
            动作队列实例
        """
        return ActionQueue(self)


class ActionQueue:
    """动作队列，用于批量执行操作"""

    def __init__(self, executor: ActionExecutor):
        """初始化动作队列"""
        self.executor = executor
        self.actions = []
        self.results = []

    def add_click_action(self, ocr_result: OCRResult, **kwargs):
        """添加点击动作"""
        self.actions.append({
            'type': 'click',
            'target': ocr_result,
            'kwargs': kwargs
        })

    def add_type_action(self, text: str, **kwargs):
        """添加输入动作"""
        self.actions.append({
            'type': 'type',
            'text': text,
            'kwargs': kwargs
        })

    def add_wait_action(self, duration: float):
        """添加等待动作"""
        self.actions.append({
            'type': 'wait',
            'duration': duration
        })

    def execute_all(self, stop_on_error: bool = False) -> List[bool]:
        """
        执行所有动作

        Args:
            stop_on_error: 遇到错误是否停止

        Returns:
            执行结果列表
        """
        self.results = []

        try:
            for i, action in enumerate(self.actions):
                logger.info(f"执行动作 {i+1}/{len(self.actions)}: {action['type']}")

                success = False

                if action['type'] == 'click':
                    success = self.executor.click_ocr_result(
                        action['target'], **action['kwargs']
                    )
                elif action['type'] == 'type':
                    success = self.executor.type_text(
                        action['text'], **action['kwargs']
                    )
                elif action['type'] == 'wait':
                    time.sleep(action['duration'])
                    success = True

                self.results.append(success)

                if not success and stop_on_error:
                    logger.error(f"动作执行失败，停止队列执行")
                    break

            success_count = sum(self.results)
            logger.info(f"动作队列执行完成，成功{success_count}/{len(self.actions)}个动作")

            return self.results

        except Exception as e:
            logger.error(f"动作队列执行失败: {e}")
            return self.results

    def clear(self):
        """清空队列"""
        self.actions.clear()
        self.results.clear()

    def get_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        if not self.results:
            return {'total': 0, 'success': 0, 'failed': 0, 'success_rate': 0.0}

        total = len(self.results)
        success = sum(self.results)
        failed = total - success
        success_rate = success / total if total > 0 else 0.0

        return {
            'total': total,
            'success': success,
            'failed': failed,
            'success_rate': success_rate
        }
