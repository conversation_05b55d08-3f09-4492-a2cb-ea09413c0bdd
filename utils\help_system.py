"""
上下文相关帮助系统
提供工具提示、帮助对话框和操作指南
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional
import webbrowser

from utils.logger import get_logger

logger = get_logger("help_system")


class HelpSystem:
    """帮助系统类"""
    
    def __init__(self):
        """初始化帮助系统"""
        self.help_data = self._load_help_data()
        self.help_window = None
        logger.info("帮助系统初始化完成")
    
    def _load_help_data(self) -> Dict[str, Dict]:
        """加载帮助数据"""
        return {
            "screenshot": {
                "title": "截图功能",
                "content": """
<h3>截图功能使用指南</h3>

<h4>1. 全屏截图</h4>
<p>• 快捷键: Ctrl+1</p>
<p>• 功能: 截取整个屏幕内容</p>
<p>• 适用场景: 全屏应用分析、桌面截图</p>
<p>• 注意事项: 会包含任务栏和其他桌面元素</p>

<h4>2. 区域截图</h4>
<p>• 快捷键: Ctrl+2</p>
<p>• 功能: 截取指定矩形区域</p>
<p>• 使用方法: 点击后输入坐标和尺寸</p>
<p>• 适用场景: 精确区域截图、重复截图</p>

<h4>3. 交互选择</h4>
<p>• 快捷键: Ctrl+3</p>
<p>• 功能: 交互式拖拽选择区域</p>
<p>• 使用方法: 拖拽鼠标选择区域，按Enter确认</p>
<p>• 适用场景: 灵活的区域选择、实时预览</p>

<h4>最佳实践</h4>
<p>• 截图前关闭不必要的窗口</p>
<p>• 确保目标内容清晰可见</p>
<p>• 避免截图包含敏感信息</p>
""",
                "tips": [
                    "截图会自动保存到data/screenshots目录",
                    "支持PNG、JPG等多种格式",
                    "可以通过文件管理器查看历史截图"
                ]
            },
            
            "ocr": {
                "title": "OCR文本识别",
                "content": """
<h3>OCR文本识别使用指南</h3>

<h4>基本使用</h4>
<p>• 快捷键: Ctrl+R</p>
<p>• 功能: 识别图像中的文本内容</p>
<p>• 支持语言: 中文、英文及混合文本</p>
<p>• 识别精度: 根据图像质量而定</p>

<h4>参数设置</h4>
<p>• 置信度阈值: 控制识别结果的可信度</p>
<p>• 语言设置: 选择主要识别语言</p>
<p>• 预处理选项: 图像增强、降噪等</p>

<h4>提高识别准确率的技巧</h4>
<p>• 确保文本清晰、对比度高</p>
<p>• 避免文字倾斜或变形</p>
<p>• 适当的图像分辨率（不要太小或太大）</p>
<p>• 避免复杂背景干扰</p>

<h4>识别结果处理</h4>
<p>• 查看置信度评分</p>
<p>• 手动校正错误识别</p>
<p>• 导出识别结果</p>
""",
                "tips": [
                    "识别速度与图像大小成正比",
                    "可以使用缓存加速重复识别",
                    "支持批量识别多个图像"
                ]
            },
            
            "search": {
                "title": "文本搜索功能",
                "content": """
<h3>文本搜索功能使用指南</h3>

<h4>搜索模式</h4>
<p><strong>精确匹配:</strong> 完全匹配指定文本</p>
<p><strong>包含匹配:</strong> 文本包含指定关键词</p>
<p><strong>模糊匹配:</strong> 允许拼写错误的容错匹配</p>
<p><strong>正则表达式:</strong> 使用正则表达式进行复杂匹配</p>
<p><strong>相似度匹配:</strong> 基于文本相似度的匹配</p>

<h4>搜索技巧</h4>
<p>• 使用合适的搜索模式</p>
<p>• 调整相似度阈值</p>
<p>• 利用搜索历史</p>
<p>• 组合多个关键词</p>

<h4>正则表达式示例</h4>
<p>• 数字: \\d+ 或 [0-9]+</p>
<p>• 邮箱: \\w+@\\w+\\.\\w+</p>
<p>• 电话: \\d{3}-\\d{4}-\\d{4}</p>
<p>• 用户ID: 用户\\d+</p>

<h4>搜索结果</h4>
<p>• 高亮显示匹配位置</p>
<p>• 显示匹配分数</p>
<p>• 支持批量操作</p>
""",
                "tips": [
                    "搜索历史会自动保存",
                    "可以导出搜索结果",
                    "支持多关键词同时搜索"
                ]
            },
            
            "automation": {
                "title": "自动化操作",
                "content": """
<h3>自动化操作使用指南</h3>

<h4>基本概念</h4>
<p>• 基于OCR结果进行自动点击</p>
<p>• 支持批量自动化操作</p>
<p>• 提供安全检查机制</p>

<h4>操作类型</h4>
<p><strong>单次点击:</strong> 点击指定文本位置</p>
<p><strong>批量点击:</strong> 依次点击多个目标</p>
<p><strong>条件操作:</strong> 根据条件执行不同操作</p>

<h4>安全注意事项</h4>
<p>• 操作前仔细确认目标</p>
<p>• 设置合适的操作间隔</p>
<p>• 启用安全模式</p>
<p>• 准备紧急停止方案</p>

<h4>最佳实践</h4>
<p>• 先进行小范围测试</p>
<p>• 记录操作日志</p>
<p>• 定期备份重要数据</p>
""",
                "tips": [
                    "可以生成自动化脚本",
                    "支持操作队列管理",
                    "提供操作历史记录"
                ]
            },
            
            "file_management": {
                "title": "文件管理",
                "content": """
<h3>文件管理使用指南</h3>

<h4>目录结构</h4>
<p>• screenshots/: 截图文件</p>
<p>• templates/: 模板图片</p>
<p>• results/: 识别结果</p>
<p>• exports/: 导出文件</p>

<h4>文件命名规范</h4>
<p>• 自动时间戳命名</p>
<p>• 支持自定义前缀和后缀</p>
<p>• 按功能类型分类存储</p>

<h4>管理功能</h4>
<p>• 文件浏览和预览</p>
<p>• 批量删除和移动</p>
<p>• 存储空间统计</p>
<p>• 自动清理旧文件</p>

<h4>维护建议</h4>
<p>• 定期清理临时文件</p>
<p>• 备份重要结果</p>
<p>• 监控存储空间使用</p>
""",
                "tips": [
                    "支持多种文件格式",
                    "可以导出文件列表",
                    "提供存储使用统计"
                ]
            }
        }
    
    def show_help(self, topic: str, parent: Optional[tk.Widget] = None):
        """
        显示帮助对话框
        
        Args:
            topic: 帮助主题
            parent: 父窗口
        """
        try:
            if topic not in self.help_data:
                messagebox.showwarning("帮助", f"未找到主题 '{topic}' 的帮助信息")
                return
            
            help_info = self.help_data[topic]
            
            # 创建帮助窗口
            if self.help_window:
                self.help_window.destroy()
            
            self.help_window = tk.Toplevel(parent)
            self.help_window.title(f"帮助 - {help_info['title']}")
            self.help_window.geometry("700x500")
            self.help_window.resizable(True, True)
            
            if parent:
                self.help_window.transient(parent)
            
            # 创建内容
            self._create_help_content(self.help_window, help_info)
            
        except Exception as e:
            logger.error(f"显示帮助失败: {e}")
            messagebox.showerror("错误", f"显示帮助失败: {e}")
    
    def _create_help_content(self, window: tk.Toplevel, help_info: Dict):
        """创建帮助内容"""
        # 主框架
        main_frame = ttk.Frame(window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text=help_info['title'], font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # 内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文本内容
        text_widget = tk.Text(
            content_frame,
            wrap=tk.WORD,
            font=('Arial', 10),
            padx=10,
            pady=10
        )
        
        # 滚动条
        scrollbar = ttk.Scrollbar(content_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 插入内容
        self._insert_formatted_content(text_widget, help_info['content'])
        
        # 提示信息
        if 'tips' in help_info:
            text_widget.insert(tk.END, "\n\n💡 实用提示:\n")
            for tip in help_info['tips']:
                text_widget.insert(tk.END, f"• {tip}\n")
        
        text_widget.configure(state=tk.DISABLED)
        
        # 按钮栏
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="关闭", command=window.destroy).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="在线文档", command=self._open_online_docs).pack(side=tk.RIGHT, padx=5)
    
    def _insert_formatted_content(self, text_widget: tk.Text, content: str):
        """插入格式化内容"""
        try:
            # 简单的HTML标签解析
            lines = content.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    text_widget.insert(tk.END, "\n")
                    continue
                
                if line.startswith('<h3>') and line.endswith('</h3>'):
                    # 三级标题
                    title = line[4:-5]
                    text_widget.insert(tk.END, f"\n{title}\n", "h3")
                    text_widget.insert(tk.END, "=" * len(title) + "\n\n")
                
                elif line.startswith('<h4>') and line.endswith('</h4>'):
                    # 四级标题
                    title = line[4:-5]
                    text_widget.insert(tk.END, f"\n{title}\n", "h4")
                    text_widget.insert(tk.END, "-" * len(title) + "\n")
                
                elif line.startswith('<p>') and line.endswith('</p>'):
                    # 段落
                    content = line[3:-4]
                    if '<strong>' in content:
                        # 处理粗体
                        parts = content.split('<strong>')
                        text_widget.insert(tk.END, parts[0])
                        for part in parts[1:]:
                            if '</strong>' in part:
                                bold_text, rest = part.split('</strong>', 1)
                                text_widget.insert(tk.END, bold_text, "bold")
                                text_widget.insert(tk.END, rest)
                            else:
                                text_widget.insert(tk.END, part)
                    else:
                        text_widget.insert(tk.END, content)
                    text_widget.insert(tk.END, "\n")
                
                else:
                    text_widget.insert(tk.END, line + "\n")
            
            # 配置标签样式
            text_widget.tag_configure("h3", font=('Arial', 12, 'bold'))
            text_widget.tag_configure("h4", font=('Arial', 11, 'bold'))
            text_widget.tag_configure("bold", font=('Arial', 10, 'bold'))
            
        except Exception as e:
            logger.warning(f"格式化内容失败: {e}")
            text_widget.insert(tk.END, content)
    
    def _open_online_docs(self):
        """打开在线文档"""
        try:
            # 这里可以替换为实际的在线文档URL
            url = "https://github.com/your-repo/docs"
            webbrowser.open(url)
        except Exception as e:
            logger.warning(f"打开在线文档失败: {e}")
    
    def get_tooltip_text(self, component: str) -> str:
        """
        获取组件的工具提示文本
        
        Args:
            component: 组件名称
            
        Returns:
            工具提示文本
        """
        tooltip_map = {
            "fullscreen_screenshot": "截取整个屏幕\n适用于全屏应用分析\n快捷键: Ctrl+1",
            "region_screenshot": "截取指定矩形区域\n适用于局部区域分析\n快捷键: Ctrl+2",
            "interactive_selection": "交互式拖拽选择区域\n提供实时预览和尺寸显示\n快捷键: Ctrl+3",
            "import_image": "从本地文件导入图像\n支持PNG、JPG、BMP等格式\n快捷键: Ctrl+O",
            "ocr_recognition": "识别图像中的文本内容\n支持中英文混合识别\n快捷键: Ctrl+R",
            "text_search": "在识别结果中搜索特定文本\n支持多种匹配模式\n快捷键: Ctrl+F",
            "template_match": "使用模板图像进行匹配\n适用于图标和界面元素识别\n快捷键: Ctrl+M",
            "click_test": "测试自动点击功能\n验证坐标定位准确性\n快捷键: Ctrl+T",
            "batch_operation": "执行批量自动化操作\n基于识别结果进行批量点击\n快捷键: Ctrl+B",
            "generate_code": "根据当前配置生成Python代码\n支持独立脚本和项目结构\n快捷键: Ctrl+G",
            "file_management": "管理截图和结果文件\n提供文件浏览和清理功能\n快捷键: Ctrl+E",
            "configuration": "打开应用程序配置对话框\n调整OCR和匹配参数\n快捷键: Ctrl+,",
            "cleanup": "清理临时文件和缓存\n释放存储空间"
        }
        
        return tooltip_map.get(component, "暂无帮助信息")
    
    def show_quick_start_guide(self, parent: Optional[tk.Widget] = None):
        """显示快速入门指南"""
        try:
            guide_window = tk.Toplevel(parent)
            guide_window.title("快速入门指南")
            guide_window.geometry("600x400")
            guide_window.resizable(False, False)
            
            if parent:
                guide_window.transient(parent)
                guide_window.grab_set()
            
            # 居中显示
            guide_window.update_idletasks()
            x = (guide_window.winfo_screenwidth() // 2) - (600 // 2)
            y = (guide_window.winfo_screenheight() // 2) - (400 // 2)
            guide_window.geometry(f"600x400+{x}+{y}")
            
            # 创建内容
            main_frame = ttk.Frame(guide_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # 标题
            title_label = ttk.Label(main_frame, text="OCR屏幕识别与自动化工具", font=('Arial', 16, 'bold'))
            title_label.pack(pady=(0, 20))
            
            # 步骤
            steps = [
                "1. 截取屏幕图像 - 使用全屏截图、区域截图或交互选择",
                "2. 执行OCR识别 - 点击'OCR识别'按钮识别文本内容",
                "3. 搜索目标文本 - 使用文本搜索功能定位特定内容",
                "4. 执行自动化操作 - 基于识别结果进行自动点击",
                "5. 管理文件 - 使用文件管理器查看和清理文件"
            ]
            
            for step in steps:
                step_label = ttk.Label(main_frame, text=step, font=('Arial', 11))
                step_label.pack(anchor=tk.W, pady=5)
            
            # 提示
            tips_frame = ttk.LabelFrame(main_frame, text="💡 快速提示", padding=10)
            tips_frame.pack(fill=tk.X, pady=(20, 0))
            
            tips = [
                "• 使用快捷键可以提高操作效率",
                "• 调整OCR置信度阈值可以改善识别效果",
                "• 定期清理临时文件以节省存储空间",
                "• 可以生成Python代码用于自动化脚本"
            ]
            
            for tip in tips:
                tip_label = ttk.Label(tips_frame, text=tip, font=('Arial', 10))
                tip_label.pack(anchor=tk.W, pady=2)
            
            # 按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(20, 0))
            
            ttk.Button(button_frame, text="开始使用", command=guide_window.destroy).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="详细帮助", 
                      command=lambda: self.show_help("screenshot", parent)).pack(side=tk.RIGHT, padx=5)
            
        except Exception as e:
            logger.error(f"显示快速入门指南失败: {e}")


# 全局帮助系统实例
help_system = HelpSystem()
