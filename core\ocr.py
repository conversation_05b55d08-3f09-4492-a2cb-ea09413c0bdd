"""
文本识别层
集成PaddleOCR，支持MKLDNN加速和高性能推理
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any, Union
import time
import re
from pathlib import Path
from enum import Enum
from dataclasses import dataclass
import difflib

from utils.config import config
from utils.logger import get_logger
from utils.performance_monitor import performance_monitor, monitor_performance

logger = get_logger("ocr")


class SearchMode(Enum):
    """文本搜索模式"""
    EXACT = "exact"          # 精确匹配
    FUZZY = "fuzzy"          # 模糊匹配
    REGEX = "regex"          # 正则表达式匹配
    CONTAINS = "contains"    # 包含匹配
    SIMILARITY = "similarity" # 相似度匹配


@dataclass
class SearchResult:
    """文本搜索结果"""
    ocr_result: 'OCRResult'
    match_score: float       # 匹配分数 (0-1)
    match_text: str         # 匹配的文本部分
    search_term: str        # 搜索词
    search_mode: SearchMode # 搜索模式

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'ocr_result': self.ocr_result.to_dict(),
            'match_score': self.match_score,
            'match_text': self.match_text,
            'search_term': self.search_term,
            'search_mode': self.search_mode.value
        }


class OCRResult:
    """OCR识别结果类"""

    def __init__(self, text: str, confidence: float, bbox: List[List[int]],
                 region_id: Optional[str] = None):
        self.text = text
        self.confidence = confidence
        self.bbox = bbox  # [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
        self.region_id = region_id or f"region_{id(self)}"
        self.is_highlighted = False  # 是否被高亮显示
        self.highlight_color = (0, 255, 0)  # 高亮颜色 (BGR)

    @property
    def center(self) -> Tuple[int, int]:
        """获取文本框中心点"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        return center_x, center_y

    @property
    def rect(self) -> Tuple[int, int, int, int]:
        """获取矩形边界框 (x, y, width, height)"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        x = min(x_coords)
        y = min(y_coords)
        width = max(x_coords) - x
        height = max(y_coords) - y
        return x, y, width, height

    @property
    def area(self) -> int:
        """获取文本区域面积"""
        _, _, width, height = self.rect
        return width * height

    @property
    def corners(self) -> Dict[str, Tuple[int, int]]:
        """获取四个角点坐标"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        return {
            'top_left': (min(x_coords), min(y_coords)),
            'top_right': (max(x_coords), min(y_coords)),
            'bottom_left': (min(x_coords), max(y_coords)),
            'bottom_right': (max(x_coords), max(y_coords))
        }

    def set_highlight(self, color: Tuple[int, int, int] = (0, 255, 0)):
        """设置高亮显示"""
        self.is_highlighted = True
        self.highlight_color = color

    def clear_highlight(self):
        """清除高亮显示"""
        self.is_highlighted = False

    def contains_point(self, x: int, y: int) -> bool:
        """检查点是否在文本区域内"""
        rect_x, rect_y, width, height = self.rect
        return (rect_x <= x <= rect_x + width and
                rect_y <= y <= rect_y + height)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'text': self.text,
            'confidence': self.confidence,
            'bbox': self.bbox,
            'center': self.center,
            'rect': self.rect,
            'area': self.area,
            'region_id': self.region_id
        }

    def __str__(self):
        return f"OCRResult(text='{self.text}', confidence={self.confidence:.3f}, center={self.center})"


class TextSearcher:
    """高级文本搜索器"""

    def __init__(self):
        self.search_history = []  # 搜索历史
        self.common_phrases = []  # 常用短语

    def search_text(self, ocr_results: List[OCRResult],
                   search_terms: Union[str, List[str]],
                   mode: SearchMode = SearchMode.CONTAINS,
                   confidence_threshold: float = 0.0,
                   similarity_threshold: float = 0.8) -> List[SearchResult]:
        """
        搜索文本

        Args:
            ocr_results: OCR识别结果列表
            search_terms: 搜索词（单个或多个）
            mode: 搜索模式
            confidence_threshold: 置信度阈值
            similarity_threshold: 相似度阈值（用于相似度匹配）

        Returns:
            搜索结果列表
        """
        if isinstance(search_terms, str):
            search_terms = [search_terms]

        all_results = []

        for search_term in search_terms:
            # 记录搜索历史
            if search_term not in self.search_history:
                self.search_history.append(search_term)
                if len(self.search_history) > 50:  # 限制历史记录数量
                    self.search_history.pop(0)

            # 搜索匹配结果
            for ocr_result in ocr_results:
                if ocr_result.confidence < confidence_threshold:
                    continue

                match_result = self._match_text(ocr_result, search_term, mode, similarity_threshold)
                if match_result:
                    all_results.append(match_result)

        # 按匹配分数排序
        all_results.sort(key=lambda x: x.match_score, reverse=True)

        logger.info(f"搜索完成，找到{len(all_results)}个匹配结果")
        return all_results

    def _match_text(self, ocr_result: OCRResult, search_term: str,
                   mode: SearchMode, similarity_threshold: float) -> Optional[SearchResult]:
        """
        匹配单个文本

        Args:
            ocr_result: OCR结果
            search_term: 搜索词
            mode: 搜索模式
            similarity_threshold: 相似度阈值

        Returns:
            搜索结果或None
        """
        text = ocr_result.text
        match_score = 0.0
        match_text = ""

        try:
            if mode == SearchMode.EXACT:
                if text == search_term:
                    match_score = 1.0
                    match_text = text

            elif mode == SearchMode.CONTAINS:
                if search_term.lower() in text.lower():
                    match_score = len(search_term) / len(text)
                    match_text = text

            elif mode == SearchMode.FUZZY:
                # 使用编辑距离计算相似度
                similarity = difflib.SequenceMatcher(None, text.lower(), search_term.lower()).ratio()
                if similarity >= similarity_threshold:
                    match_score = similarity
                    match_text = text

            elif mode == SearchMode.REGEX:
                pattern = re.compile(search_term, re.IGNORECASE)
                match = pattern.search(text)
                if match:
                    match_score = len(match.group()) / len(text)
                    match_text = match.group()

            elif mode == SearchMode.SIMILARITY:
                # 使用difflib计算相似度
                similarity = difflib.SequenceMatcher(None, text.lower(), search_term.lower()).ratio()
                if similarity >= similarity_threshold:
                    match_score = similarity
                    match_text = text

            if match_score > 0:
                return SearchResult(
                    ocr_result=ocr_result,
                    match_score=match_score,
                    match_text=match_text,
                    search_term=search_term,
                    search_mode=mode
                )

        except Exception as e:
            logger.warning(f"文本匹配失败: {e}")

        return None

    def get_search_history(self) -> List[str]:
        """获取搜索历史"""
        return self.search_history.copy()

    def clear_search_history(self):
        """清除搜索历史"""
        self.search_history.clear()

    def add_common_phrase(self, phrase: str):
        """添加常用短语"""
        if phrase not in self.common_phrases:
            self.common_phrases.append(phrase)

    def get_common_phrases(self) -> List[str]:
        """获取常用短语"""
        return self.common_phrases.copy()


class OCREngine:
    """OCR文本识别引擎"""

    def __init__(self):
        """初始化OCR引擎"""
        self.config = config.get_section('ocr')
        self.ocr = None
        self.text_searcher = TextSearcher()  # 文本搜索器
        self._result_cache = {}  # 结果缓存
        self._cache_max_size = 100  # 最大缓存数量
        self._init_ocr()

        logger.info("OCREngine初始化完成")
    
    def _init_ocr(self) -> None:
        """初始化PaddleOCR"""
        try:
            from paddleocr import PaddleOCR
            
            # 创建OCR实例
            self.ocr = PaddleOCR(
                use_angle_cls=self.config.get('use_angle_cls', True),
                lang=self.config.get('lang', 'ch'),
                enable_mkldnn=self.config.get('enable_mkldnn', True),
                use_gpu=self.config.get('use_gpu', False),
                det_db_thresh=self.config.get('det_db_thresh', 0.3),
                det_db_box_thresh=self.config.get('det_db_box_thresh', 0.6),
                det_db_unclip_ratio=self.config.get('det_db_unclip_ratio', 1.5),
                show_log=False
            )
            
            logger.info("PaddleOCR初始化成功")
            
        except ImportError:
            logger.error("PaddleOCR未安装，请运行: pip install paddlepaddle paddleocr")
            raise
        except Exception as e:
            logger.error(f"PaddleOCR初始化失败: {e}")
            raise

    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理优化

        Args:
            image: 输入图像

        Returns:
            预处理后的图像
        """
        try:
            # 获取原始尺寸
            height, width = image.shape[:2]

            # 如果图像太大，进行缩放以提高处理速度
            max_dimension = 1920  # 最大尺寸
            if max(height, width) > max_dimension:
                scale = max_dimension / max(height, width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.debug(f"图像已缩放: {width}x{height} -> {new_width}x{new_height}")

            # 转换为灰度图像（可选，根据配置决定）
            if self.config.get('use_grayscale', False):
                if len(image.shape) == 3:
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                    # 增强对比度
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                    enhanced = clahe.apply(gray)
                    # 转回BGR格式
                    image = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)

            # 降噪处理（可选）
            if self.config.get('denoise', False):
                image = cv2.bilateralFilter(image, 9, 75, 75)

            return image

        except Exception as e:
            logger.warning(f"图像预处理失败: {e}")
            return image

    def _get_image_hash(self, image: np.ndarray) -> str:
        """
        计算图像哈希值用于缓存

        Args:
            image: 输入图像

        Returns:
            图像哈希值
        """
        try:
            # 使用图像的形状和部分像素值计算简单哈希
            height, width = image.shape[:2]

            # 取图像的几个关键点
            sample_points = [
                image[height//4, width//4],
                image[height//2, width//2],
                image[3*height//4, 3*width//4]
            ]

            # 计算哈希
            import hashlib
            hash_input = f"{height}x{width}_{sample_points}".encode()
            return hashlib.md5(hash_input).hexdigest()[:16]

        except Exception as e:
            logger.warning(f"计算图像哈希失败: {e}")
            return str(time.time())  # 使用时间戳作为备用
    
    def recognize_text(self, image: np.ndarray,
                      confidence_threshold: Optional[float] = None,
                      use_cache: bool = True) -> List[OCRResult]:
        """
        识别图像中的文本（优化版本）

        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            use_cache: 是否使用缓存

        Returns:
            OCR识别结果列表
        """
        if self.ocr is None:
            logger.error("OCR引擎未初始化")
            return []

        try:
            confidence_threshold = confidence_threshold or \
                                 self.config.get('confidence_threshold', 0.8)

            # 开始性能监控
            operation_id = performance_monitor.start_operation(
                "ocr_recognition",
                {
                    'image_shape': image.shape,
                    'confidence_threshold': confidence_threshold,
                    'use_cache': use_cache
                }
            )

            # 检查缓存
            if use_cache:
                image_hash = self._get_image_hash(image)
                cache_key = f"{image_hash}_{confidence_threshold}"

                if cache_key in self._result_cache:
                    logger.debug("使用缓存的OCR结果")
                    performance_monitor.end_operation(operation_id, {'cache_hit': True})
                    return self._result_cache[cache_key]

            start_time = time.time()

            # 图像预处理
            processed_image = self._preprocess_image(image)

            # 执行OCR识别
            results = self.ocr.ocr(processed_image, cls=True)

            elapsed_time = (time.time() - start_time) * 1000
            logger.debug(f"OCR识别耗时: {elapsed_time:.2f}ms")

            # 解析结果
            ocr_results = []

            if results and results[0]:
                for line in results[0]:
                    bbox = line[0]
                    text_info = line[1]
                    text = text_info[0]
                    confidence = text_info[1]

                    # 过滤低置信度结果
                    if confidence >= confidence_threshold:
                        ocr_result = OCRResult(text, confidence, bbox)
                        ocr_results.append(ocr_result)
                        logger.debug(f"识别文本: {text} (置信度: {confidence:.3f})")

            logger.info(f"OCR识别完成，找到{len(ocr_results)}个文本区域")

            # 缓存结果
            if use_cache:
                self._cache_result(cache_key, ocr_results)

            # 结束性能监控
            performance_monitor.end_operation(operation_id, {
                'cache_hit': False,
                'results_count': len(ocr_results),
                'processing_time': elapsed_time
            })

            return ocr_results

        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            # 结束性能监控（失败情况）
            performance_monitor.end_operation(operation_id, {
                'success': False,
                'error': str(e)
            })
            return []

    def _cache_result(self, cache_key: str, result: List[OCRResult]):
        """
        缓存OCR结果

        Args:
            cache_key: 缓存键
            result: OCR结果
        """
        try:
            # 如果缓存已满，删除最旧的条目
            if len(self._result_cache) >= self._cache_max_size:
                # 删除第一个（最旧的）条目
                oldest_key = next(iter(self._result_cache))
                del self._result_cache[oldest_key]
                logger.debug(f"删除旧缓存条目: {oldest_key}")

            self._result_cache[cache_key] = result
            logger.debug(f"缓存OCR结果: {cache_key}")

        except Exception as e:
            logger.warning(f"缓存结果失败: {e}")

    def clear_cache(self):
        """清除OCR结果缓存"""
        self._result_cache.clear()
        logger.info("OCR结果缓存已清除")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._result_cache),
            'max_size': self._cache_max_size,
            'cache_keys': list(self._result_cache.keys())
        }
    
    def find_text(self, image: np.ndarray, target_text: str,
                  confidence_threshold: Optional[float] = None,
                  fuzzy_match: bool = False) -> List[OCRResult]:
        """
        查找指定文本
        
        Args:
            image: 输入图像
            target_text: 目标文本
            confidence_threshold: 置信度阈值
            fuzzy_match: 是否启用模糊匹配
            
        Returns:
            匹配的OCR结果列表
        """
        try:
            # 识别所有文本
            all_results = self.recognize_text(image, confidence_threshold)
            
            # 查找匹配的文本
            matched_results = []
            for result in all_results:
                if fuzzy_match:
                    # 模糊匹配
                    if target_text.lower() in result.text.lower():
                        matched_results.append(result)
                else:
                    # 精确匹配
                    if target_text == result.text:
                        matched_results.append(result)
            
            logger.info(f"找到{len(matched_results)}个匹配的文本: '{target_text}'")
            return matched_results
            
        except Exception as e:
            logger.error(f"查找文本失败: {e}")
            return []

    def advanced_search(self, image: np.ndarray,
                       search_terms: Union[str, List[str]],
                       mode: SearchMode = SearchMode.CONTAINS,
                       confidence_threshold: Optional[float] = None,
                       similarity_threshold: float = 0.8,
                       max_results: Optional[int] = None) -> List[SearchResult]:
        """
        高级文本搜索

        Args:
            image: 输入图像
            search_terms: 搜索词（单个或多个）
            mode: 搜索模式
            confidence_threshold: 置信度阈值
            similarity_threshold: 相似度阈值
            max_results: 最大结果数量

        Returns:
            搜索结果列表
        """
        try:
            # 识别所有文本
            ocr_results = self.recognize_text(image, confidence_threshold)

            if not ocr_results:
                logger.warning("未识别到任何文本")
                return []

            # 执行搜索
            search_results = self.text_searcher.search_text(
                ocr_results=ocr_results,
                search_terms=search_terms,
                mode=mode,
                confidence_threshold=confidence_threshold or 0.0,
                similarity_threshold=similarity_threshold
            )

            # 限制结果数量
            if max_results and len(search_results) > max_results:
                search_results = search_results[:max_results]

            # 设置高亮
            for result in search_results:
                result.ocr_result.set_highlight()

            logger.info(f"高级搜索完成，找到{len(search_results)}个匹配结果")
            return search_results

        except Exception as e:
            logger.error(f"高级搜索失败: {e}")
            return []

    def batch_search(self, image: np.ndarray,
                    search_configs: List[Dict[str, Any]]) -> Dict[str, List[SearchResult]]:
        """
        批量搜索

        Args:
            image: 输入图像
            search_configs: 搜索配置列表，每个配置包含搜索参数

        Returns:
            搜索结果字典，键为搜索词，值为结果列表
        """
        try:
            # 识别所有文本（只识别一次）
            ocr_results = self.recognize_text(image)

            if not ocr_results:
                logger.warning("未识别到任何文本")
                return {}

            batch_results = {}

            for config in search_configs:
                search_term = config.get('search_term', '')
                mode = SearchMode(config.get('mode', 'contains'))
                confidence_threshold = config.get('confidence_threshold', 0.0)
                similarity_threshold = config.get('similarity_threshold', 0.8)

                if not search_term:
                    continue

                # 执行单个搜索
                results = self.text_searcher.search_text(
                    ocr_results=ocr_results,
                    search_terms=search_term,
                    mode=mode,
                    confidence_threshold=confidence_threshold,
                    similarity_threshold=similarity_threshold
                )

                batch_results[search_term] = results

                # 设置不同颜色的高亮
                color_map = {
                    0: (0, 255, 0),    # 绿色
                    1: (255, 0, 0),    # 蓝色
                    2: (0, 0, 255),    # 红色
                    3: (255, 255, 0),  # 青色
                    4: (255, 0, 255),  # 品红色
                }
                color_index = len(batch_results) % len(color_map)

                for result in results:
                    result.ocr_result.set_highlight(color_map[color_index])

            total_results = sum(len(results) for results in batch_results.values())
            logger.info(f"批量搜索完成，总共找到{total_results}个匹配结果")
            return batch_results

        except Exception as e:
            logger.error(f"批量搜索失败: {e}")
            return {}

    def get_search_statistics(self, search_results: List[SearchResult]) -> Dict[str, Any]:
        """
        获取搜索统计信息

        Args:
            search_results: 搜索结果列表

        Returns:
            统计信息字典
        """
        if not search_results:
            return {}

        try:
            # 计算统计信息
            total_results = len(search_results)
            avg_confidence = sum(r.ocr_result.confidence for r in search_results) / total_results
            avg_match_score = sum(r.match_score for r in search_results) / total_results

            # 按搜索模式分组
            mode_counts = {}
            for result in search_results:
                mode = result.search_mode.value
                mode_counts[mode] = mode_counts.get(mode, 0) + 1

            # 置信度分布
            confidence_ranges = {
                'high': len([r for r in search_results if r.ocr_result.confidence >= 0.9]),
                'medium': len([r for r in search_results if 0.7 <= r.ocr_result.confidence < 0.9]),
                'low': len([r for r in search_results if r.ocr_result.confidence < 0.7])
            }

            return {
                'total_results': total_results,
                'average_confidence': avg_confidence,
                'average_match_score': avg_match_score,
                'mode_distribution': mode_counts,
                'confidence_distribution': confidence_ranges,
                'best_match': max(search_results, key=lambda x: x.match_score).to_dict() if search_results else None
            }

        except Exception as e:
            logger.error(f"获取搜索统计信息失败: {e}")
            return {}
    
    def get_text_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        获取文本区域的矩形框
        
        Args:
            image: 输入图像
            
        Returns:
            矩形框列表 [(x, y, width, height), ...]
        """
        try:
            results = self.recognize_text(image)
            regions = [result.rect for result in results]
            
            logger.debug(f"获取到{len(regions)}个文本区域")
            return regions
            
        except Exception as e:
            logger.error(f"获取文本区域失败: {e}")
            return []
    
    def extract_text_from_region(self, image: np.ndarray, 
                                region: Tuple[int, int, int, int]) -> str:
        """
        从指定区域提取文本
        
        Args:
            image: 输入图像
            region: 区域 (x, y, width, height)
            
        Returns:
            提取的文本
        """
        try:
            x, y, width, height = region
            
            # 裁剪区域
            roi = image[y:y+height, x:x+width]
            
            # 识别文本
            results = self.recognize_text(roi)
            
            # 合并所有文本
            texts = [result.text for result in results]
            combined_text = ' '.join(texts)
            
            logger.debug(f"从区域{region}提取文本: '{combined_text}'")
            return combined_text
            
        except Exception as e:
            logger.error(f"从区域提取文本失败: {e}")
            return ""
    
    def robust_recognize(self, image: np.ndarray, max_retries: int = 3) -> List[OCRResult]:
        """
        稳健的OCR识别，包含重试机制
        
        Args:
            image: 输入图像
            max_retries: 最大重试次数
            
        Returns:
            OCR识别结果列表
        """
        for attempt in range(max_retries):
            try:
                results = self.recognize_text(image)
                if results:
                    return results
                    
                logger.warning(f"OCR识别结果为空，第{attempt+1}次重试")
                time.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"OCR识别失败，第{attempt+1}次重试: {e}")
                time.sleep(0.1)
        
        logger.error(f"OCR识别失败，已重试{max_retries}次")
        return []
    
    def visualize_results(self, image: np.ndarray, results: List[OCRResult],
                         save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化OCR识别结果
        
        Args:
            image: 原始图像
            results: OCR结果列表
            save_path: 保存路径
            
        Returns:
            标注后的图像
        """
        try:
            # 复制图像
            vis_image = image.copy()
            
            # 绘制文本框和文本
            for i, result in enumerate(results):
                # 绘制边界框
                bbox = np.array(result.bbox, dtype=np.int32)
                cv2.polylines(vis_image, [bbox], True, (0, 255, 0), 2)
                
                # 绘制文本
                x, y = result.center
                text = f"{result.text} ({result.confidence:.2f})"
                cv2.putText(vis_image, text, (x-50, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            # 保存图像
            if save_path:
                Path(save_path).parent.mkdir(parents=True, exist_ok=True)
                cv2.imwrite(save_path, vis_image)
                logger.info(f"可视化结果已保存: {save_path}")
            
            return vis_image

        except Exception as e:
            logger.error(f"可视化OCR结果失败: {e}")
            return image

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            "engine": "PaddleOCR",
            "mkldnn_enabled": self.config.get('enable_mkldnn', True),
            "gpu_enabled": self.config.get('use_gpu', False),
            "language": self.config.get('lang', 'ch')
        }
