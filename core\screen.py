"""
截图与预处理层
提供全屏/区域截图、灰度化、二值化、形态学等预处理功能
"""

import cv2
import numpy as np
import pyautogui
from PIL import Image
from typing import Optional, Tuple, Union
from pathlib import Path
import time

from utils.config import config
from utils.logger import get_logger

logger = get_logger("screen")


class ScreenCapture:
    """屏幕截图与预处理类"""
    
    def __init__(self):
        """初始化截图类"""
        self.config = config.get_section('screenshot')
        self.preprocessing_config = config.get_section('preprocessing')
        
        # 设置pyautogui参数
        pyautogui.FAILSAFE = config.get('action.fail_safe', True)
        pyautogui.PAUSE = config.get('action.pause', 0.1)
        
        logger.info("ScreenCapture初始化完成")
    
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None,
                      save_path: Optional[str] = None) -> np.ndarray:
        """
        截取屏幕
        
        Args:
            region: 截图区域 (x, y, width, height)，None表示全屏
            save_path: 保存路径，None表示不保存
            
        Returns:
            截图的numpy数组 (BGR格式)
        """
        try:
            # 获取截图区域
            region = region or self.config.get('region')
            
            # 截图
            if region:
                screenshot = pyautogui.screenshot(region=region)
                logger.debug(f"区域截图完成: {region}")
            else:
                screenshot = pyautogui.screenshot()
                logger.debug("全屏截图完成")
            
            # 转换为numpy数组 (PIL -> RGB -> BGR)
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            # 保存截图
            if save_path:
                self._save_screenshot(screenshot_bgr, save_path)
            
            return screenshot_bgr
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            raise

    def capture_region(self, x: int, y: int, width: int, height: int,
                      save_path: Optional[str] = None) -> Optional[np.ndarray]:
        """
        截取指定区域

        Args:
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度
            save_path: 保存路径

        Returns:
            截图图像数组
        """
        try:
            return self.capture_screen(region=(x, y, width, height), save_path=save_path)
        except Exception as e:
            logger.error(f"区域截图失败: {e}")
            return None
    
    def capture_window(self, window_title: str, 
                      save_path: Optional[str] = None) -> Optional[np.ndarray]:
        """
        截取指定窗口
        
        Args:
            window_title: 窗口标题
            save_path: 保存路径
            
        Returns:
            截图的numpy数组或None
        """
        try:
            import pygetwindow as gw
            
            # 查找窗口
            windows = gw.getWindowsWithTitle(window_title)
            if not windows:
                logger.warning(f"未找到窗口: {window_title}")
                return None
            
            window = windows[0]
            
            # 激活窗口
            if window.isMinimized:
                window.restore()
            window.activate()
            
            time.sleep(0.1)  # 等待窗口激活
            
            # 获取窗口区域
            region = (window.left, window.top, window.width, window.height)
            
            return self.capture_screen(region, save_path)
            
        except ImportError:
            logger.error("需要安装pygetwindow: pip install pygetwindow")
            return None
        except Exception as e:
            logger.error(f"窗口截图失败: {e}")
            return None
    
    def preprocess_image(self, image: np.ndarray, 
                        enable_tophat: Optional[bool] = None,
                        enable_sobel: Optional[bool] = None,
                        enable_binary: Optional[bool] = None) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            enable_tophat: 是否启用顶帽变换
            enable_sobel: 是否启用Sobel边缘检测
            enable_binary: 是否启用二值化
            
        Returns:
            预处理后的图像
        """
        try:
            # 使用配置中的默认值
            enable_tophat = enable_tophat if enable_tophat is not None else \
                           self.preprocessing_config.get('enable_tophat', True)
            enable_sobel = enable_sobel if enable_sobel is not None else \
                          self.preprocessing_config.get('enable_sobel', True)
            enable_binary = enable_binary if enable_binary is not None else \
                           self.preprocessing_config.get('enable_binary', True)
            
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            processed = gray.copy()
            
            # 顶帽变换 - 突出文字
            if enable_tophat:
                kernel_size = self.preprocessing_config.get('kernel_size', (17, 17))
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size)
                tophat = cv2.morphologyEx(gray, cv2.MORPH_TOPHAT, kernel)
                processed = tophat
                logger.debug("应用顶帽变换")
            
            # Sobel边缘检测
            if enable_sobel:
                sobel_y = cv2.Sobel(processed, cv2.CV_16S, 1, 0)
                sobel_abs = cv2.convertScaleAbs(sobel_y)
                processed = sobel_abs
                logger.debug("应用Sobel边缘检测")
            
            # 二值化
            if enable_binary:
                threshold = self.preprocessing_config.get('binary_threshold', 127)
                if enable_sobel:
                    # Sobel后使用不同的阈值
                    threshold = self.preprocessing_config.get('sobel_threshold', 75)
                
                _, binary = cv2.threshold(processed, threshold, 255, cv2.THRESH_BINARY)
                processed = binary
                logger.debug(f"应用二值化，阈值: {threshold}")
            
            return processed
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return image
    
    def enhance_text_regions(self, image: np.ndarray) -> np.ndarray:
        """
        增强文本区域
        
        Args:
            image: 输入图像
            
        Returns:
            增强后的图像
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 高斯滤波去噪
            denoised = cv2.GaussianBlur(enhanced, (3, 3), 0)
            
            logger.debug("文本区域增强完成")
            return denoised
            
        except Exception as e:
            logger.error(f"文本区域增强失败: {e}")
            return image
    
    def _save_screenshot(self, image: np.ndarray, save_path: str) -> None:
        """保存截图"""
        try:
            # 确保目录存在
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存图像
            cv2.imwrite(save_path, image)
            logger.info(f"截图已保存: {save_path}")
            
        except Exception as e:
            logger.error(f"保存截图失败: {e}")
    
    def get_screen_size(self) -> Tuple[int, int]:
        """获取屏幕尺寸"""
        try:
            size = pyautogui.size()
            return size.width, size.height
        except Exception as e:
            logger.error(f"获取屏幕尺寸失败: {e}")
            return (1920, 1080)  # 默认值
    
    def cleanup_temp_screenshots(self) -> None:
        """清理临时截图文件"""
        try:
            temp_dir = Path(config.get('paths.screenshots_dir', 'temp/screenshots'))
            if temp_dir.exists():
                for img_file in temp_dir.glob('*.png'):
                    # 删除超过1小时的临时截图
                    if time.time() - img_file.stat().st_mtime > 3600:
                        img_file.unlink()
                        logger.debug(f"清理临时截图: {img_file}")
        except Exception as e:
            logger.error(f"清理临时截图失败: {e}")
