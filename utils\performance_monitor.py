"""
性能监控工具
监控OCR处理速度、内存使用等性能指标
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from collections import deque
import gc

from utils.logger import get_logger

logger = get_logger("performance_monitor")


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: float
    operation: str
    duration: float
    memory_usage: float
    cpu_usage: float
    details: Dict[str, Any]


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_history: 最大历史记录数量
        """
        self.max_history = max_history
        self.metrics_history = deque(maxlen=max_history)
        self.current_operations = {}
        self.monitoring_enabled = True
        
        # 系统信息
        self.process = psutil.Process()
        
        logger.info("性能监控器已初始化")
    
    def start_operation(self, operation_name: str, details: Optional[Dict[str, Any]] = None) -> str:
        """
        开始监控一个操作
        
        Args:
            operation_name: 操作名称
            details: 操作详细信息
            
        Returns:
            操作ID
        """
        if not self.monitoring_enabled:
            return ""
        
        try:
            operation_id = f"{operation_name}_{int(time.time() * 1000000)}"
            
            self.current_operations[operation_id] = {
                'name': operation_name,
                'start_time': time.time(),
                'start_memory': self.process.memory_info().rss / 1024 / 1024,  # MB
                'start_cpu': self.process.cpu_percent(),
                'details': details or {}
            }
            
            logger.debug(f"开始监控操作: {operation_name}")
            return operation_id
            
        except Exception as e:
            logger.warning(f"开始监控操作失败: {e}")
            return ""
    
    def end_operation(self, operation_id: str, additional_details: Optional[Dict[str, Any]] = None):
        """
        结束监控一个操作
        
        Args:
            operation_id: 操作ID
            additional_details: 额外的详细信息
        """
        if not self.monitoring_enabled or not operation_id:
            return
        
        try:
            if operation_id not in self.current_operations:
                logger.warning(f"未找到操作ID: {operation_id}")
                return
            
            operation = self.current_operations[operation_id]
            end_time = time.time()
            
            # 计算性能指标
            duration = end_time - operation['start_time']
            end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            memory_usage = end_memory - operation['start_memory']
            cpu_usage = self.process.cpu_percent()
            
            # 合并详细信息
            details = operation['details'].copy()
            if additional_details:
                details.update(additional_details)
            
            # 创建性能指标
            metric = PerformanceMetric(
                timestamp=end_time,
                operation=operation['name'],
                duration=duration,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                details=details
            )
            
            # 添加到历史记录
            self.metrics_history.append(metric)
            
            # 清理当前操作
            del self.current_operations[operation_id]
            
            logger.debug(f"操作完成: {operation['name']}, 耗时: {duration:.3f}s, 内存变化: {memory_usage:.2f}MB")
            
        except Exception as e:
            logger.warning(f"结束监控操作失败: {e}")
    
    def get_performance_summary(self, operation_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Args:
            operation_filter: 操作过滤器
            
        Returns:
            性能摘要
        """
        try:
            if not self.metrics_history:
                return {'message': '没有性能数据'}
            
            # 过滤指标
            filtered_metrics = []
            for metric in self.metrics_history:
                if operation_filter is None or operation_filter in metric.operation:
                    filtered_metrics.append(metric)
            
            if not filtered_metrics:
                return {'message': f'没有找到操作 "{operation_filter}" 的性能数据'}
            
            # 计算统计信息
            durations = [m.duration for m in filtered_metrics]
            memory_usages = [m.memory_usage for m in filtered_metrics]
            cpu_usages = [m.cpu_usage for m in filtered_metrics]
            
            summary = {
                'total_operations': len(filtered_metrics),
                'time_range': {
                    'start': min(m.timestamp for m in filtered_metrics),
                    'end': max(m.timestamp for m in filtered_metrics)
                },
                'duration_stats': {
                    'min': min(durations),
                    'max': max(durations),
                    'avg': sum(durations) / len(durations),
                    'total': sum(durations)
                },
                'memory_stats': {
                    'min': min(memory_usages),
                    'max': max(memory_usages),
                    'avg': sum(memory_usages) / len(memory_usages),
                    'total': sum(memory_usages)
                },
                'cpu_stats': {
                    'min': min(cpu_usages),
                    'max': max(cpu_usages),
                    'avg': sum(cpu_usages) / len(cpu_usages)
                },
                'operation_counts': {}
            }
            
            # 统计各操作的数量
            for metric in filtered_metrics:
                op_name = metric.operation
                summary['operation_counts'][op_name] = summary['operation_counts'].get(op_name, 0) + 1
            
            return summary
            
        except Exception as e:
            logger.error(f"获取性能摘要失败: {e}")
            return {'error': str(e)}
    
    def get_recent_metrics(self, count: int = 10) -> List[PerformanceMetric]:
        """
        获取最近的性能指标
        
        Args:
            count: 返回的指标数量
            
        Returns:
            最近的性能指标列表
        """
        try:
            return list(self.metrics_history)[-count:]
        except Exception as e:
            logger.warning(f"获取最近指标失败: {e}")
            return []
    
    def clear_history(self):
        """清除历史记录"""
        try:
            self.metrics_history.clear()
            logger.info("性能监控历史记录已清除")
        except Exception as e:
            logger.warning(f"清除历史记录失败: {e}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
                'memory_available': psutil.virtual_memory().available / 1024 / 1024 / 1024,  # GB
                'memory_percent': psutil.virtual_memory().percent,
                'process_memory': self.process.memory_info().rss / 1024 / 1024,  # MB
                'process_cpu': self.process.cpu_percent()
            }
        except Exception as e:
            logger.warning(f"获取系统信息失败: {e}")
            return {}
    
    def optimize_memory(self):
        """优化内存使用"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            
            # 获取内存信息
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            logger.info(f"内存优化完成，回收对象: {collected}, 当前内存: {memory_mb:.2f}MB")
            
            return {
                'collected_objects': collected,
                'current_memory_mb': memory_mb
            }
            
        except Exception as e:
            logger.warning(f"内存优化失败: {e}")
            return {}
    
    def enable_monitoring(self):
        """启用监控"""
        self.monitoring_enabled = True
        logger.info("性能监控已启用")
    
    def disable_monitoring(self):
        """禁用监控"""
        self.monitoring_enabled = False
        logger.info("性能监控已禁用")
    
    def export_metrics(self, filename: str):
        """
        导出性能指标到文件
        
        Args:
            filename: 文件名
        """
        try:
            import json
            
            metrics_data = []
            for metric in self.metrics_history:
                metrics_data.append({
                    'timestamp': metric.timestamp,
                    'operation': metric.operation,
                    'duration': metric.duration,
                    'memory_usage': metric.memory_usage,
                    'cpu_usage': metric.cpu_usage,
                    'details': metric.details
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"性能指标已导出到: {filename}")
            
        except Exception as e:
            logger.error(f"导出性能指标失败: {e}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def monitor_performance(operation_name: str, details: Optional[Dict[str, Any]] = None):
    """
    性能监控装饰器
    
    Args:
        operation_name: 操作名称
        details: 操作详细信息
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            operation_id = performance_monitor.start_operation(operation_name, details)
            try:
                result = func(*args, **kwargs)
                performance_monitor.end_operation(operation_id, {'success': True})
                return result
            except Exception as e:
                performance_monitor.end_operation(operation_id, {'success': False, 'error': str(e)})
                raise
        return wrapper
    return decorator
