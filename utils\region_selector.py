"""
区域选择工具
提供交互式屏幕区域选择功能
"""

import tkinter as tk
from tkinter import messagebox
import pyautogui
import cv2
import numpy as np
from typing import Tuple, Optional, Callable
import threading
import time

from utils.logger import get_logger

logger = get_logger("region_selector")


class RegionSelector:
    """交互式区域选择器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        """
        初始化区域选择器
        
        Args:
            callback: 选择完成后的回调函数，接收(x, y, width, height)参数
        """
        self.callback = callback
        self.selection_window = None
        self.canvas = None
        self.start_x = 0
        self.start_y = 0
        self.end_x = 0
        self.end_y = 0
        self.is_selecting = False
        self.selection_rect = None
        self.screenshot = None
        
    def start_selection(self):
        """开始区域选择"""
        try:
            logger.info("开始区域选择")
            
            # 隐藏主窗口（如果存在）
            self._hide_main_window()
            
            # 等待一下让窗口完全隐藏
            time.sleep(0.5)
            
            # 截取全屏
            self.screenshot = pyautogui.screenshot()
            screenshot_array = np.array(self.screenshot)
            screenshot_array = cv2.cvtColor(screenshot_array, cv2.COLOR_RGB2BGR)
            
            # 创建选择窗口
            self._create_selection_window()
            
        except Exception as e:
            logger.error(f"开始区域选择失败: {e}")
            messagebox.showerror("错误", f"区域选择失败: {e}")
    
    def _hide_main_window(self):
        """隐藏主窗口"""
        try:
            # 获取所有tkinter窗口并最小化
            import tkinter as tk
            for widget in tk._default_root.winfo_children():
                if isinstance(widget, tk.Toplevel):
                    widget.withdraw()
            if tk._default_root:
                tk._default_root.withdraw()
        except:
            pass  # 如果没有主窗口就忽略
    
    def _show_main_window(self):
        """显示主窗口"""
        try:
            import tkinter as tk
            if tk._default_root:
                tk._default_root.deiconify()
            for widget in tk._default_root.winfo_children():
                if isinstance(widget, tk.Toplevel):
                    widget.deiconify()
        except:
            pass
    
    def _create_selection_window(self):
        """创建选择窗口"""
        try:
            # 创建全屏透明窗口
            self.selection_window = tk.Toplevel()
            self.selection_window.title("区域选择 - 拖拽选择区域，按ESC取消")
            
            # 设置窗口属性
            self.selection_window.attributes('-fullscreen', True)
            self.selection_window.attributes('-topmost', True)
            self.selection_window.attributes('-alpha', 0.3)  # 半透明
            self.selection_window.configure(bg='black')
            
            # 创建画布
            self.canvas = tk.Canvas(
                self.selection_window,
                highlightthickness=0,
                bg='black',
                cursor='crosshair'
            )
            self.canvas.pack(fill=tk.BOTH, expand=True)
            
            # 绑定事件
            self.canvas.bind('<Button-1>', self._on_mouse_down)
            self.canvas.bind('<B1-Motion>', self._on_mouse_drag)
            self.canvas.bind('<ButtonRelease-1>', self._on_mouse_up)
            self.selection_window.bind('<Escape>', self._on_escape)
            self.selection_window.bind('<Return>', self._on_confirm)
            
            # 设置焦点
            self.selection_window.focus_set()
            
            # 添加提示文本
            self._add_instructions()
            
            logger.info("区域选择窗口已创建")
            
        except Exception as e:
            logger.error(f"创建选择窗口失败: {e}")
            self._cleanup()
    
    def _add_instructions(self):
        """添加操作说明"""
        try:
            instructions = [
                "拖拽鼠标选择区域",
                "按 Enter 确认选择",
                "按 ESC 取消选择"
            ]
            
            y_offset = 50
            for instruction in instructions:
                self.canvas.create_text(
                    50, y_offset,
                    text=instruction,
                    fill='white',
                    font=('Arial', 14),
                    anchor='nw'
                )
                y_offset += 30
                
        except Exception as e:
            logger.warning(f"添加操作说明失败: {e}")
    
    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        try:
            self.start_x = event.x
            self.start_y = event.y
            self.is_selecting = True
            
            # 清除之前的选择
            if self.selection_rect:
                self.canvas.delete(self.selection_rect)
                
        except Exception as e:
            logger.warning(f"鼠标按下事件处理失败: {e}")
    
    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        try:
            if not self.is_selecting:
                return
            
            self.end_x = event.x
            self.end_y = event.y
            
            # 清除之前的矩形
            if self.selection_rect:
                self.canvas.delete(self.selection_rect)
            
            # 绘制新的选择矩形
            self.selection_rect = self.canvas.create_rectangle(
                self.start_x, self.start_y,
                self.end_x, self.end_y,
                outline='red',
                width=2,
                fill='',
                dash=(5, 5)
            )
            
            # 显示尺寸信息
            width = abs(self.end_x - self.start_x)
            height = abs(self.end_y - self.start_y)
            size_text = f"尺寸: {width} x {height}"
            
            # 删除之前的尺寸文本
            self.canvas.delete("size_text")
            
            # 添加新的尺寸文本
            text_x = min(self.start_x, self.end_x) + 5
            text_y = min(self.start_y, self.end_y) - 25
            if text_y < 0:
                text_y = min(self.start_y, self.end_y) + 5
            
            self.canvas.create_text(
                text_x, text_y,
                text=size_text,
                fill='yellow',
                font=('Arial', 12, 'bold'),
                anchor='nw',
                tags="size_text"
            )
            
        except Exception as e:
            logger.warning(f"鼠标拖拽事件处理失败: {e}")
    
    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        try:
            self.is_selecting = False
            self.end_x = event.x
            self.end_y = event.y
            
            # 计算选择区域
            x = min(self.start_x, self.end_x)
            y = min(self.start_y, self.end_y)
            width = abs(self.end_x - self.start_x)
            height = abs(self.end_y - self.start_y)
            
            # 检查选择区域是否有效
            if width < 10 or height < 10:
                logger.warning("选择区域太小，请重新选择")
                return
            
            logger.info(f"选择区域: ({x}, {y}, {width}, {height})")
            
            # 添加确认提示
            self._show_confirmation(x, y, width, height)
            
        except Exception as e:
            logger.warning(f"鼠标释放事件处理失败: {e}")
    
    def _show_confirmation(self, x: int, y: int, width: int, height: int):
        """显示确认信息"""
        try:
            # 删除之前的确认文本
            self.canvas.delete("confirm_text")
            
            # 添加确认文本
            confirm_text = f"选择区域: ({x}, {y}) 尺寸: {width}x{height}\n按 Enter 确认，ESC 取消"
            
            self.canvas.create_text(
                x + width // 2, y + height + 30,
                text=confirm_text,
                fill='lime',
                font=('Arial', 14, 'bold'),
                anchor='center',
                tags="confirm_text"
            )
            
        except Exception as e:
            logger.warning(f"显示确认信息失败: {e}")
    
    def _on_confirm(self, event):
        """确认选择"""
        try:
            if not self.selection_rect:
                logger.warning("没有选择区域")
                return
            
            # 计算最终选择区域
            x = min(self.start_x, self.end_x)
            y = min(self.start_y, self.end_y)
            width = abs(self.end_x - self.start_x)
            height = abs(self.end_y - self.start_y)
            
            logger.info(f"确认选择区域: ({x}, {y}, {width}, {height})")
            
            # 调用回调函数
            if self.callback:
                self.callback(x, y, width, height)
            
            # 清理窗口
            self._cleanup()
            
        except Exception as e:
            logger.error(f"确认选择失败: {e}")
            self._cleanup()
    
    def _on_escape(self, event):
        """取消选择"""
        logger.info("用户取消区域选择")
        self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.selection_window:
                self.selection_window.destroy()
                self.selection_window = None
            
            # 恢复主窗口
            self._show_main_window()
            
            logger.info("区域选择器已清理")
            
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")


def select_screen_region(callback: Optional[Callable] = None) -> Optional[Tuple[int, int, int, int]]:
    """
    选择屏幕区域的便捷函数
    
    Args:
        callback: 选择完成后的回调函数
        
    Returns:
        选择的区域坐标 (x, y, width, height) 或 None
    """
    try:
        selector = RegionSelector(callback)
        selector.start_selection()
        return None  # 异步操作，结果通过回调返回
        
    except Exception as e:
        logger.error(f"区域选择失败: {e}")
        return None
