"""
OCR文本搜索性能测试脚本
测试不同图像大小和搜索模式下的性能表现
"""

import time
import os
import cv2
import numpy as np
from typing import Dict, List, Tuple
import json
from pathlib import Path

from core.ocr import OCREngine, SearchMode
from utils.logger import get_logger

logger = get_logger("search_performance")


class SearchPerformanceTest:
    """搜索性能测试类"""
    
    def __init__(self):
        """初始化性能测试"""
        self.ocr_engine = OCREngine()
        self.test_results = {}
        self.test_images = {}
        
    def create_test_images(self):
        """创建不同大小的测试图像"""
        logger.info("创建测试图像...")
        
        # 创建测试目录
        test_dir = Path("temp/performance_test")
        test_dir.mkdir(parents=True, exist_ok=True)
        
        # 测试文本内容
        test_texts = [
            "Welcome to OCR Performance Test",
            "欢迎使用OCR性能测试工具",
            "登录系统 Login System",
            "用户名 Username: <EMAIL>",
            "密码 Password: ********",
            "提交 Submit 取消 Cancel",
            "设置 Settings 帮助 Help",
            "版本信息 Version: 2.0.1",
            "联系我们 Contact Us",
            "退出应用 Exit Application",
            "搜索功能 Search Function",
            "文件管理 File Management",
            "数据导出 Data Export",
            "系统配置 System Configuration",
            "性能监控 Performance Monitor"
        ]
        
        # 创建小图像 (<500KB)
        small_image = self._create_text_image(test_texts[:5], (800, 400))
        small_path = test_dir / "small_test.png"
        cv2.imwrite(str(small_path), small_image)
        self.test_images['small'] = {
            'path': str(small_path),
            'image': small_image,
            'size': os.path.getsize(small_path),
            'dimensions': small_image.shape[:2]
        }
        
        # 创建中图像 (500KB-2MB)
        medium_image = self._create_text_image(test_texts[:10], (1200, 800))
        medium_path = test_dir / "medium_test.png"
        cv2.imwrite(str(medium_path), medium_image)
        self.test_images['medium'] = {
            'path': str(medium_path),
            'image': medium_image,
            'size': os.path.getsize(medium_path),
            'dimensions': medium_image.shape[:2]
        }
        
        # 创建大图像 (>2MB)
        large_image = self._create_text_image(test_texts, (1920, 1200))
        large_path = test_dir / "large_test.png"
        cv2.imwrite(str(large_path), large_image)
        self.test_images['large'] = {
            'path': str(large_path),
            'image': large_image,
            'size': os.path.getsize(large_path),
            'dimensions': large_image.shape[:2]
        }
        
        logger.info("测试图像创建完成")
        for size_type, info in self.test_images.items():
            size_mb = info['size'] / (1024 * 1024)
            logger.info(f"{size_type}: {info['dimensions']} - {size_mb:.2f}MB")
    
    def _create_text_image(self, texts: List[str], size: Tuple[int, int]) -> np.ndarray:
        """创建包含文本的测试图像"""
        height, width = size[1], size[0]
        image = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        color = (0, 0, 0)
        thickness = 2
        
        y_offset = 50
        line_height = 40
        
        for i, text in enumerate(texts):
            y = y_offset + i * line_height
            if y > height - 30:
                break
            cv2.putText(image, text, (50, y), font, font_scale, color, thickness)
        
        return image
    
    def run_performance_tests(self) -> Dict:
        """运行性能测试"""
        logger.info("开始性能测试...")
        
        # 创建测试图像
        self.create_test_images()
        
        # 测试搜索词
        search_terms = [
            "OCR",           # 英文精确匹配
            "用户名",         # 中文精确匹配
            "Login",         # 英文包含匹配
            "设置",          # 中文包含匹配
            "admin"          # 模糊匹配测试
        ]
        
        # 测试搜索模式
        search_modes = [
            SearchMode.EXACT,
            SearchMode.CONTAINS,
            SearchMode.FUZZY,
            SearchMode.REGEX,
            SearchMode.SIMILARITY
        ]
        
        results = {}
        
        for size_type, image_info in self.test_images.items():
            logger.info(f"测试图像大小: {size_type}")
            results[size_type] = {}
            
            image = image_info['image']
            
            # 首先进行OCR识别
            ocr_start = time.time()
            ocr_results = self.ocr_engine.recognize_text(image, confidence_threshold=0.8)
            ocr_time = time.time() - ocr_start
            
            results[size_type]['ocr_time'] = ocr_time
            results[size_type]['ocr_results_count'] = len(ocr_results)
            results[size_type]['image_info'] = {
                'size_bytes': image_info['size'],
                'size_mb': image_info['size'] / (1024 * 1024),
                'dimensions': image_info['dimensions']
            }
            
            # 测试不同搜索模式
            for mode in search_modes:
                mode_name = mode.value
                results[size_type][mode_name] = {}
                
                for search_term in search_terms:
                    logger.info(f"  测试搜索: {search_term} (模式: {mode_name})")
                    
                    # 测试搜索性能
                    search_start = time.time()
                    search_results = self.ocr_engine.advanced_search(
                        image=image,
                        search_terms=search_term,
                        mode=mode,
                        confidence_threshold=0.8,
                        similarity_threshold=0.8
                    )
                    search_time = time.time() - search_start
                    
                    # 模拟结果显示时间
                    display_start = time.time()
                    # 这里模拟结果处理和显示的时间
                    time.sleep(0.001 * len(search_results))  # 模拟显示延迟
                    display_time = time.time() - display_start
                    
                    total_time = search_time + display_time
                    
                    results[size_type][mode_name][search_term] = {
                        'search_time': search_time,
                        'display_time': display_time,
                        'total_time': total_time,
                        'results_count': len(search_results),
                        'found': len(search_results) > 0
                    }
        
        self.test_results = results
        return results
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        if not self.test_results:
            logger.error("没有测试结果可生成报告")
            return
        
        report_path = "temp/search_performance_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# OCR文本搜索性能测试报告\n\n")
            f.write(f"**测试时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 测试概览
            f.write("## 测试概览\n\n")
            f.write("| 图像类型 | 文件大小 | 图像尺寸 | OCR识别时间(s) | 识别结果数 |\n")
            f.write("|---------|---------|---------|---------------|----------|\n")
            
            for size_type, results in self.test_results.items():
                info = results['image_info']
                f.write(f"| {size_type} | {info['size_mb']:.2f}MB | {info['dimensions'][1]}x{info['dimensions'][0]} | {results['ocr_time']:.3f} | {results['ocr_results_count']} |\n")
            
            # 详细性能数据
            f.write("\n## 详细性能数据\n\n")
            
            for size_type, results in self.test_results.items():
                f.write(f"### {size_type.upper()}图像性能\n\n")
                
                # 按搜索模式统计
                for mode in ['exact', 'contains', 'fuzzy', 'regex', 'similarity']:
                    if mode in results:
                        f.write(f"#### {mode.upper()}模式\n\n")
                        f.write("| 搜索词 | 搜索时间(s) | 显示时间(s) | 总时间(s) | 结果数 | 是否找到 |\n")
                        f.write("|-------|------------|------------|----------|-------|--------|\n")
                        
                        mode_results = results[mode]
                        for term, data in mode_results.items():
                            found_icon = "✅" if data['found'] else "❌"
                            f.write(f"| {term} | {data['search_time']:.3f} | {data['display_time']:.3f} | {data['total_time']:.3f} | {data['results_count']} | {found_icon} |\n")
                        f.write("\n")
            
            # 性能分析
            f.write("## 性能分析\n\n")
            self._write_performance_analysis(f)
            
            # 优化建议
            f.write("## 优化建议\n\n")
            self._write_optimization_suggestions(f)
        
        # 保存JSON格式的详细数据
        json_path = "temp/search_performance_data.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"性能测试报告已生成: {report_path}")
        logger.info(f"详细数据已保存: {json_path}")
    
    def _write_performance_analysis(self, f):
        """写入性能分析"""
        f.write("### OCR识别性能\n\n")
        
        ocr_times = []
        for size_type, results in self.test_results.items():
            ocr_times.append((size_type, results['ocr_time'], results['image_info']['size_mb']))
        
        f.write("OCR识别时间与图像大小的关系：\n\n")
        for size_type, ocr_time, size_mb in ocr_times:
            f.write(f"- **{size_type}图像** ({size_mb:.2f}MB): {ocr_time:.3f}秒\n")
        
        f.write("\n### 搜索模式性能对比\n\n")
        
        # 计算各模式平均性能
        mode_avg_times = {}
        for size_type, results in self.test_results.items():
            for mode in ['exact', 'contains', 'fuzzy', 'regex', 'similarity']:
                if mode in results:
                    if mode not in mode_avg_times:
                        mode_avg_times[mode] = []
                    
                    mode_results = results[mode]
                    avg_time = sum(data['total_time'] for data in mode_results.values()) / len(mode_results)
                    mode_avg_times[mode].append(avg_time)
        
        f.write("各搜索模式平均响应时间：\n\n")
        for mode, times in mode_avg_times.items():
            avg_time = sum(times) / len(times)
            f.write(f"- **{mode.upper()}模式**: {avg_time:.3f}秒\n")
    
    def _write_optimization_suggestions(self, f):
        """写入优化建议"""
        f.write("### 基于测试结果的优化建议\n\n")
        
        # 分析OCR性能
        ocr_times = [results['ocr_time'] for results in self.test_results.values()]
        max_ocr_time = max(ocr_times)
        
        if max_ocr_time > 2.0:
            f.write("1. **OCR性能优化**:\n")
            f.write("   - OCR识别时间较长，建议实现图像预处理优化\n")
            f.write("   - 考虑添加图像缓存机制\n")
            f.write("   - 对大图像实现分块处理\n\n")
        
        # 分析搜索性能
        f.write("2. **搜索性能优化**:\n")
        f.write("   - 实现搜索结果缓存\n")
        f.write("   - 优化正则表达式搜索算法\n")
        f.write("   - 添加搜索索引机制\n\n")
        
        f.write("3. **用户体验优化**:\n")
        f.write("   - 添加搜索进度指示器\n")
        f.write("   - 实现异步搜索避免界面阻塞\n")
        f.write("   - 提供搜索取消功能\n\n")


def main():
    """主测试函数"""
    print("开始OCR文本搜索性能测试...")
    
    # 创建测试目录
    os.makedirs("temp", exist_ok=True)
    
    # 运行性能测试
    test = SearchPerformanceTest()
    results = test.run_performance_tests()
    
    # 生成报告
    test.generate_performance_report()
    
    print("性能测试完成！")
    print("报告文件: temp/search_performance_report.md")
    print("数据文件: temp/search_performance_data.json")


if __name__ == "__main__":
    main()
