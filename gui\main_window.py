"""
主窗口界面
实现MainWindow类，包含主界面布局、菜单栏、工具栏和状态栏
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import time
from typing import Optional, Dict, Any, List
from pathlib import Path
import cv2
import numpy as np
from PIL import Image, ImageTk

from utils.config import config
from utils.logger import get_logger
from core.screen import ScreenCapture
from core.ocr import OCREngine, SearchMode, SearchResult
from core.match import TemplateMatch
from core.action import ActionExecutor
from utils.visualization import ImageAnnotator
from utils.region_selector import RegionSelector
from utils.code_generator import CodeGenerator
from utils.enhanced_toolbar import EnhancedToolbar, ToolbarButton, ToolbarGroup, create_default_toolbar_config
from utils.enhanced_image_viewer import EnhancedImageViewer
from utils.file_manager import FileManager
from utils.help_system import help_system
from gui.file_manager_dialog import FileManagerDialog

logger = get_logger("gui")


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.setup_window()
        
        # 初始化核心组件
        self.screen_capture = ScreenCapture()
        self.ocr_engine = OCREngine()
        self.template_match = TemplateMatch()
        self.action_executor = ActionExecutor()
        self.image_annotator = ImageAnnotator()
        self.code_generator = CodeGenerator()
        self.file_manager = FileManager()

        # 增强组件
        self.enhanced_toolbar = None
        self.enhanced_image_viewer = None

        # GUI组件
        self.log_queue = queue.Queue()
        self.current_image = None
        self.current_results = []
        self.current_search_results = []
        self.search_history = []
        
        # 创建界面
        self.create_menu()
        self.create_toolbar()
        self.create_main_frame()
        self.create_status_bar()

        # 设置快捷键绑定
        self.setup_shortcuts()

        # 启动日志处理线程
        self.start_log_handler()
        
        logger.info("主窗口初始化完成")
    
    def setup_window(self):
        """设置窗口属性"""
        gui_config = config.get_section('gui')
        
        # 窗口标题
        title = gui_config.get('window_title', '屏幕识别与自动化工具')
        self.root.title(title)
        
        # 窗口大小
        window_size = gui_config.get('window_size', '1200x800')
        self.root.geometry(window_size)
        
        # 窗口图标（如果存在）
        icon_path = Path('assets/icon.ico')
        if icon_path.exists():
            self.root.iconbitmap(str(icon_path))
        
        # 设置最小尺寸
        self.root.minsize(800, 600)
        
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开图片", command=self.open_image)
        file_menu.add_command(label="保存截图", command=self.save_screenshot)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="全屏截图", command=self.capture_fullscreen)
        tools_menu.add_command(label="区域截图", command=self.capture_region)
        tools_menu.add_separator()
        tools_menu.add_command(label="OCR识别", command=self.run_ocr)
        tools_menu.add_command(label="模板匹配", command=self.run_template_match)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="配置参数", command=self.open_config_dialog)
        settings_menu.add_command(label="清理缓存", command=self.clear_cache)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建增强工具栏"""
        # 创建增强工具栏
        self.enhanced_toolbar = EnhancedToolbar(self.root)

        # 获取默认配置
        toolbar_groups = create_default_toolbar_config()

        # 先更新按钮命令，再添加按钮组
        self._update_toolbar_commands(toolbar_groups)

        # 添加按钮组
        for group in toolbar_groups:
            self.enhanced_toolbar.add_group(group)

        logger.info("增强工具栏创建完成")

    def _update_toolbar_commands(self, toolbar_groups: List[ToolbarGroup]):
        """更新工具栏按钮命令"""
        command_map = {
            "全屏截图": self.capture_fullscreen,
            "区域截图": self.capture_region,
            "交互选择": self.interactive_region_capture,
            "导入图像": self.import_image,
            "OCR识别": self.run_ocr,
            "文本搜索": self.show_text_search,
            "模板匹配": self.run_template_match,
            "点击测试": self.test_click,
            "批量操作": self.batch_click_results,
            "生成代码": self.generate_code,
            "文件管理": self.show_file_manager,
            "配置": self.open_config_dialog,
            "清理": self.clear_cache
        }

        # 更新按钮命令
        for group in toolbar_groups:
            for button in group.buttons:
                if button.text in command_map:
                    button.command = command_map[button.text]
                else:
                    button.command = lambda t=button.text: self._show_not_implemented(t)
    
    def create_main_frame(self):
        """创建主框架"""
        # 主框架使用PanedWindow分割
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板 - 控制和日志
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # 右侧面板 - 图像显示
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        self.create_left_panel(left_frame)
        self.create_right_panel(right_frame)
    
    def create_left_panel(self, parent):
        """创建左侧面板"""
        # 控制面板
        control_frame = ttk.LabelFrame(parent, text="控制面板")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 截图控制
        screenshot_frame = ttk.Frame(control_frame)
        screenshot_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(screenshot_frame, text="截图区域:").pack(side=tk.LEFT)
        self.region_var = tk.StringVar(value="全屏")
        region_combo = ttk.Combobox(screenshot_frame, textvariable=self.region_var,
                                   values=["全屏", "自定义区域"], state="readonly")
        region_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # OCR控制
        ocr_frame = ttk.Frame(control_frame)
        ocr_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(ocr_frame, text="置信度:").pack(side=tk.LEFT)
        self.confidence_var = tk.DoubleVar(value=0.8)
        confidence_scale = ttk.Scale(ocr_frame, from_=0.1, to=1.0, 
                                   variable=self.confidence_var, orient=tk.HORIZONTAL)
        confidence_scale.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        confidence_label = ttk.Label(ocr_frame, text="0.8")
        confidence_label.pack(side=tk.LEFT)
        
        # 更新置信度显示
        def update_confidence_label(*args):
            confidence_label.config(text=f"{self.confidence_var.get():.2f}")
        self.confidence_var.trace('w', update_confidence_label)

        # 文本搜索面板
        self.create_search_panel(control_frame)

        # 日志面板
        log_frame = ttk.LabelFrame(parent, text="运行日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置日志颜色
        self.log_text.tag_config("INFO", foreground="black")
        self.log_text.tag_config("DEBUG", foreground="gray")
        self.log_text.tag_config("WARNING", foreground="orange")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("CRITICAL", foreground="red", background="yellow")
    
    def create_right_panel(self, parent):
        """创建右侧面板"""
        # 图像显示面板
        image_frame = ttk.LabelFrame(parent, text="图像显示")
        image_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 使用增强的图像预览器
        self.enhanced_image_viewer = EnhancedImageViewer(image_frame)

        # 设置回调函数
        self.enhanced_image_viewer.set_click_callback(self._on_image_click)
        self.enhanced_image_viewer.set_coordinate_callback(self._on_coordinate_change)

        # 保持兼容性的引用
        self.image_canvas = self.enhanced_image_viewer.canvas
        
        # 结果面板
        result_frame = ttk.LabelFrame(parent, text="识别结果")
        result_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 结果列表
        self.result_tree = ttk.Treeview(result_frame, columns=("类型", "内容", "置信度", "坐标"), 
                                       show="headings", height=6)
        
        self.result_tree.heading("类型", text="类型")
        self.result_tree.heading("内容", text="内容")
        self.result_tree.heading("置信度", text="置信度")
        self.result_tree.heading("坐标", text="坐标")
        
        self.result_tree.column("类型", width=80)
        self.result_tree.column("内容", width=200)
        self.result_tree.column("置信度", width=80)
        self.result_tree.column("坐标", width=120)
        
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, 
                                        command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 双击事件
        self.result_tree.bind("<Double-1>", self.on_result_double_click)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 状态变量和标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_bar, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 进度条
        self.progress_bar = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress_bar.pack(side=tk.RIGHT, padx=5)

    def start_log_handler(self):
        """启动日志处理线程"""
        def log_handler():
            while True:
                try:
                    record = self.log_queue.get(timeout=0.1)
                    if record is None:
                        break
                    self.add_log_message(record)
                except queue.Empty:
                    continue

        self.log_thread = threading.Thread(target=log_handler, daemon=True)
        self.log_thread.start()

    def add_log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message, level)
        self.log_text.see(tk.END)

        # 限制日志行数
        max_lines = config.get('gui.log_lines_limit', 1000)
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > max_lines:
            self.log_text.delete('1.0', f'{lines - max_lines}.0')

    def set_status(self, message: str, show_progress: bool = False):
        """设置状态栏消息"""
        self.status_label.config(text=message)

        if show_progress:
            self.progress_bar.start()
        else:
            self.progress_bar.stop()

    def capture_fullscreen(self):
        """全屏截图"""
        def capture():
            try:
                self.set_status("正在截图...", True)
                screenshot = self.screen_capture.capture_screen()
                self.display_image(screenshot)
                self.add_log_message("全屏截图完成")
                self.set_status("截图完成")
            except Exception as e:
                self.add_log_message(f"截图失败: {e}", "ERROR")
                self.set_status("截图失败")

        threading.Thread(target=capture, daemon=True).start()

    def capture_region(self):
        """区域截图"""
        # 这里可以实现区域选择对话框
        messagebox.showinfo("提示", "区域截图功能待实现")

    def run_ocr(self):
        """运行OCR识别"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先截图")
            return

        def ocr_task():
            try:
                self.set_status("正在进行OCR识别...", True)
                confidence = self.confidence_var.get()
                results = self.ocr_engine.recognize_text(self.current_image, confidence)

                self.current_results = results
                self.update_result_tree(results, "OCR")
                self.add_log_message(f"OCR识别完成，找到{len(results)}个文本")
                self.set_status("OCR识别完成")

            except Exception as e:
                self.add_log_message(f"OCR识别失败: {e}", "ERROR")
                self.set_status("OCR识别失败")

        threading.Thread(target=ocr_task, daemon=True).start()

    def run_template_match(self):
        """运行模板匹配"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先截图")
            return

        # 选择模板文件
        template_path = filedialog.askopenfilename(
            title="选择模板图片",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp")]
        )

        if not template_path:
            return

        def match_task():
            try:
                self.set_status("正在进行模板匹配...", True)
                matches = self.template_match.find_template(self.current_image, template_path)

                self.current_results = matches
                self.update_result_tree(matches, "模板匹配")
                self.add_log_message(f"模板匹配完成，找到{len(matches)}个匹配")
                self.set_status("模板匹配完成")

            except Exception as e:
                self.add_log_message(f"模板匹配失败: {e}", "ERROR")
                self.set_status("模板匹配失败")

        threading.Thread(target=match_task, daemon=True).start()

    def test_click(self):
        """测试点击"""
        if not self.current_results:
            messagebox.showwarning("警告", "没有可点击的结果")
            return

        # 点击第一个结果的中心
        result = self.current_results[0]
        if hasattr(result, 'center'):
            x, y = result.center
            success = self.action_executor.click(x, y)
            if success:
                self.add_log_message(f"点击成功: ({x}, {y})")
            else:
                self.add_log_message("点击失败", "ERROR")

    def display_image(self, image: np.ndarray):
        """显示图像"""
        try:
            self.current_image = image

            # 使用增强的图像预览器
            if self.enhanced_image_viewer:
                self.enhanced_image_viewer.load_image(image)
            else:
                # 兼容性处理：如果增强预览器不可用，使用原始方法
                self._display_image_fallback(image)

        except Exception as e:
            self.add_log_message(f"显示图像失败: {e}", "ERROR")

    def update_result_tree(self, results, result_type: str):
        """更新结果树"""
        # 清空现有结果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 添加新结果
        for i, result in enumerate(results):
            if result_type == "OCR":
                content = result.text
                confidence = f"{result.confidence:.3f}"
                center = result.center
            else:  # 模板匹配
                content = f"匹配{i+1}"
                confidence = f"{result.confidence:.3f}"
                center = result.center

            coords = f"({center[0]}, {center[1]})"

            self.result_tree.insert("", tk.END, values=(result_type, content, confidence, coords))

    def on_result_double_click(self, event):
        """结果双击事件"""
        selection = self.result_tree.selection()
        if selection and self.current_results:
            item = self.result_tree.item(selection[0])
            index = self.result_tree.index(selection[0])

            if index < len(self.current_results):
                result = self.current_results[index]
                x, y = result.center

                # 询问是否点击
                if messagebox.askyesno("确认", f"是否点击坐标 ({x}, {y})?"):
                    success = self.action_executor.click(x, y)
                    if success:
                        self.add_log_message(f"点击成功: ({x}, {y})")
                    else:
                        self.add_log_message("点击失败", "ERROR")

    def open_image(self):
        """打开图片文件"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp")]
        )

        if file_path:
            try:
                image = cv2.imread(file_path)
                if image is not None:
                    self.display_image(image)
                    self.add_log_message(f"打开图片: {file_path}")
                else:
                    messagebox.showerror("错误", "无法打开图片文件")
            except Exception as e:
                messagebox.showerror("错误", f"打开图片失败: {e}")

    def save_screenshot(self):
        """保存截图"""
        if self.current_image is None:
            messagebox.showwarning("警告", "没有可保存的图片")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存截图",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("JPEG文件", "*.jpg"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                cv2.imwrite(file_path, self.current_image)
                self.add_log_message(f"截图已保存: {file_path}")
                messagebox.showinfo("成功", "截图保存成功")
            except Exception as e:
                messagebox.showerror("错误", f"保存截图失败: {e}")

    def open_config_dialog(self):
        """打开配置对话框"""
        try:
            from .config_dialog import ConfigDialog
            dialog = ConfigDialog(self.root)
            self.add_log_message("打开配置对话框")
        except ImportError:
            messagebox.showinfo("提示", "配置对话框功能待实现")

    def clear_cache(self):
        """清理缓存"""
        try:
            self.template_match.clear_cache()
            self.action_executor.cleanup_temp_files()
            self.add_log_message("缓存清理完成")
            messagebox.showinfo("成功", "缓存清理完成")
        except Exception as e:
            self.add_log_message(f"清理缓存失败: {e}", "ERROR")
            messagebox.showerror("错误", f"清理缓存失败: {e}")

    def show_help(self):
        """显示帮助"""
        help_text = """
屏幕识别与自动化工具使用说明

1. 截图功能：
   - 全屏截图：捕获整个屏幕
   - 区域截图：捕获指定区域（待实现）

2. OCR识别：
   - 对当前图片进行文字识别
   - 可调整置信度阈值

3. 模板匹配：
   - 在当前图片中查找模板图片
   - 支持多尺度匹配

4. 自动点击：
   - 双击识别结果可自动点击对应位置
   - 支持测试点击功能

5. 配置管理：
   - 可调整各种参数
   - 支持保存和加载配置
        """
        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """
屏幕识别与自动化工具 v1.0

基于五层架构设计：
- 截图与预处理层
- 文本识别层（PaddleOCR）
- 图像匹配层（OpenCV）
- 动作执行层（PyAutoGUI）
- 控制与日志层

开发团队：AutomationTeam
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 停止日志处理线程
            self.log_queue.put(None)

            # 清理资源
            self.action_executor.cleanup_temp_files()

            # 保存配置
            config.save_config()

            self.root.destroy()

        except Exception as e:
            logger.error(f"关闭窗口时出错: {e}")
            self.root.destroy()

    def create_search_panel(self, parent):
        """创建文本搜索面板"""
        search_frame = ttk.LabelFrame(parent, text="文本搜索")
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        # 搜索输入框
        input_frame = ttk.Frame(search_frame)
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(input_frame, text="搜索文本:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(input_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.search_entry.bind('<Return>', lambda e: self.perform_text_search())

        # 搜索按钮
        ttk.Button(input_frame, text="搜索",
                  command=self.perform_text_search).pack(side=tk.RIGHT, padx=2)

        # 搜索选项
        options_frame = ttk.Frame(search_frame)
        options_frame.pack(fill=tk.X, padx=5, pady=2)

        # 搜索模式
        ttk.Label(options_frame, text="模式:").pack(side=tk.LEFT)
        self.search_mode_var = tk.StringVar(value="contains")
        mode_combo = ttk.Combobox(options_frame, textvariable=self.search_mode_var,
                                 values=["exact", "contains", "fuzzy", "regex", "similarity"],
                                 state="readonly", width=10)
        mode_combo.pack(side=tk.LEFT, padx=5)

        # 相似度阈值
        ttk.Label(options_frame, text="相似度:").pack(side=tk.LEFT, padx=(10, 0))
        self.similarity_var = tk.DoubleVar(value=0.8)
        similarity_scale = ttk.Scale(options_frame, from_=0.1, to=1.0,
                                   variable=self.similarity_var, orient=tk.HORIZONTAL,
                                   length=80)
        similarity_scale.pack(side=tk.LEFT, padx=5)

        # 搜索历史
        history_frame = ttk.Frame(search_frame)
        history_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(history_frame, text="历史:").pack(side=tk.LEFT)
        self.history_combo = ttk.Combobox(history_frame, state="readonly")
        self.history_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.history_combo.bind('<<ComboboxSelected>>', self.on_history_selected)

        # 操作按钮
        action_frame = ttk.Frame(search_frame)
        action_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(action_frame, text="清除结果",
                  command=self.clear_search_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(action_frame, text="导出结果",
                  command=self.export_search_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(action_frame, text="批量点击",
                  command=self.batch_click_results).pack(side=tk.RIGHT, padx=2)

    def show_text_search(self):
        """显示文本搜索对话框"""
        if not self.current_image is None:
            self.perform_text_search()
        else:
            messagebox.showwarning("警告", "请先截图或加载图像")

    def perform_text_search(self):
        """执行文本搜索"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先截图或加载图像")
            return

        search_text = self.search_var.get().strip()
        if not search_text:
            messagebox.showwarning("警告", "请输入搜索文本")
            return

        try:
            # 显示进度条
            self.progress_bar.start()
            self.status_var.set("正在执行文本搜索...")
            self.root.update()

            # 获取搜索参数
            mode = SearchMode(self.search_mode_var.get())
            similarity_threshold = self.similarity_var.get()
            confidence_threshold = self.confidence_var.get()

            # 执行搜索
            self.add_log_message(f"开始搜索文本: '{search_text}'")

            search_results = self.ocr_engine.advanced_search(
                image=self.current_image,
                search_terms=search_text,
                mode=mode,
                confidence_threshold=confidence_threshold,
                similarity_threshold=similarity_threshold
            )

            self.current_search_results = search_results

            # 更新搜索历史
            if search_text not in self.search_history:
                self.search_history.append(search_text)
                if len(self.search_history) > 20:  # 限制历史记录数量
                    self.search_history.pop(0)
                self.update_search_history()

            # 显示结果
            self.display_search_results(search_results)

            # 更新图像显示
            self.update_image_display_with_search_results(search_results)

            self.add_log_message(f"搜索完成，找到{len(search_results)}个匹配结果")
            self.status_var.set(f"搜索完成 - 找到 {len(search_results)} 个结果")

        except Exception as e:
            logger.error(f"文本搜索失败: {e}")
            messagebox.showerror("错误", f"文本搜索失败: {e}")
            self.status_var.set("搜索失败")
        finally:
            # 停止进度条
            self.progress_bar.stop()

    def display_search_results(self, search_results: list):
        """显示搜索结果"""
        try:
            # 清空现有结果
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)

            # 添加搜索结果
            for i, result in enumerate(search_results):
                ocr_result = result.ocr_result
                center_x, center_y = ocr_result.center

                self.result_tree.insert("", "end", values=(
                    "搜索结果",
                    f"{result.search_term} -> {result.match_text}",
                    f"{result.match_score:.3f}",
                    f"({center_x}, {center_y})"
                ))

            # 更新状态栏
            self.status_var.set(f"找到 {len(search_results)} 个搜索结果")

        except Exception as e:
            logger.error(f"显示搜索结果失败: {e}")

    def update_image_display_with_search_results(self, search_results: list):
        """更新图像显示，包含搜索结果标注"""
        try:
            if self.current_image is None:
                return

            # 如果使用增强图像预览器，添加标注
            if self.enhanced_image_viewer:
                # 转换搜索结果为标注格式
                annotations = []
                for i, result in enumerate(search_results):
                    bbox = result.ocr_result.bbox
                    x = min([p[0] for p in bbox])
                    y = min([p[1] for p in bbox])
                    w = max([p[0] for p in bbox]) - x
                    h = max([p[1] for p in bbox]) - y

                    # 使用不同颜色标记不同结果
                    colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0),
                             (255, 0, 255), (0, 255, 255), (128, 255, 0), (255, 128, 0)]
                    color = colors[i % len(colors)]

                    annotations.append({
                        'type': 'rectangle',
                        'bbox': (x, y, w, h),
                        'color': color,
                        'thickness': 2
                    })

                    # 添加文本标签
                    annotations.append({
                        'type': 'text',
                        'text': f"{result.match_text[:10]}...",
                        'position': (x, y - 5),
                        'color': color,
                        'font_scale': 0.6,
                        'thickness': 1
                    })

                self.enhanced_image_viewer.add_annotations(annotations)
            else:
                # 兼容性处理：使用原始标注方法
                annotated_image = self.image_annotator.annotate_search_results(
                    self.current_image, search_results
                )
                self.display_image(annotated_image)

        except Exception as e:
            logger.error(f"更新图像显示失败: {e}")

    def on_history_selected(self, event):
        """选择搜索历史"""
        selected = self.history_combo.get()
        if selected:
            self.search_var.set(selected)

    def update_search_history(self):
        """更新搜索历史下拉框"""
        self.history_combo['values'] = self.search_history

    def clear_search_results(self):
        """清除搜索结果"""
        self.current_search_results = []

        # 清空结果列表
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 恢复原始图像显示
        if self.current_image is not None:
            self.display_image(self.current_image)

        self.status_var.set("搜索结果已清除")
        self.add_log_message("搜索结果已清除")

    def export_search_results(self):
        """导出搜索结果"""
        if not self.current_search_results:
            messagebox.showwarning("警告", "没有搜索结果可导出")
            return

        try:
            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title="导出搜索结果",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("搜索结果导出\n")
                    f.write("=" * 50 + "\n\n")

                    for i, result in enumerate(self.current_search_results):
                        ocr_result = result.ocr_result
                        center_x, center_y = ocr_result.center

                        f.write(f"结果 {i+1}:\n")
                        f.write(f"  搜索词: {result.search_term}\n")
                        f.write(f"  匹配文本: {result.match_text}\n")
                        f.write(f"  匹配分数: {result.match_score:.3f}\n")
                        f.write(f"  置信度: {ocr_result.confidence:.3f}\n")
                        f.write(f"  中心坐标: ({center_x}, {center_y})\n")
                        f.write(f"  搜索模式: {result.search_mode.value}\n")
                        f.write("\n")

                self.add_log_message(f"搜索结果已导出到: {filename}")
                messagebox.showinfo("成功", f"搜索结果已导出到:\n{filename}")

        except Exception as e:
            logger.error(f"导出搜索结果失败: {e}")
            messagebox.showerror("错误", f"导出失败: {e}")

    def batch_click_results(self):
        """批量点击搜索结果"""
        if not self.current_search_results:
            messagebox.showwarning("警告", "没有搜索结果可点击")
            return

        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认批量点击",
                f"将依次点击 {len(self.current_search_results)} 个搜索结果，是否继续？"
            )

            if result:
                # 执行批量点击
                click_results = self.action_executor.batch_click_search_results(
                    self.current_search_results,
                    click_delay=1.0
                )

                success_count = sum(click_results.values())
                self.add_log_message(f"批量点击完成，成功点击{success_count}个结果")
                messagebox.showinfo("完成", f"批量点击完成\n成功: {success_count}/{len(self.current_search_results)}")

        except Exception as e:
            logger.error(f"批量点击失败: {e}")
            messagebox.showerror("错误", f"批量点击失败: {e}")

    def interactive_region_capture(self):
        """交互式区域截图"""
        try:
            def on_region_selected(x, y, width, height):
                """区域选择完成回调"""
                try:
                    # 截取选定区域
                    region_image = self.screen_capture.capture_region(x, y, width, height)

                    if region_image is not None:
                        self.current_image = region_image
                        self.display_image(region_image)
                        self.add_log_message(f"交互式区域截图完成: ({x}, {y}, {width}, {height})")
                        self.status_var.set(f"区域截图完成 - 尺寸: {width}x{height}")
                    else:
                        self.add_log_message("区域截图失败")
                        messagebox.showerror("错误", "区域截图失败")

                except Exception as e:
                    logger.error(f"区域截图回调失败: {e}")
                    messagebox.showerror("错误", f"区域截图失败: {e}")

            # 创建区域选择器
            selector = RegionSelector(callback=on_region_selected)
            selector.start_selection()

        except Exception as e:
            logger.error(f"交互式区域截图失败: {e}")
            messagebox.showerror("错误", f"交互式区域截图失败: {e}")

    def import_image(self):
        """导入图像文件"""
        try:
            # 文件对话框
            filename = filedialog.askopenfilename(
                title="选择图像文件",
                filetypes=[
                    ("所有支持的图像", "*.png *.jpg *.jpeg *.bmp *.tiff *.tif"),
                    ("PNG文件", "*.png"),
                    ("JPEG文件", "*.jpg *.jpeg"),
                    ("BMP文件", "*.bmp"),
                    ("TIFF文件", "*.tiff *.tif"),
                    ("所有文件", "*.*")
                ]
            )

            if filename:
                # 加载图像
                import cv2
                image = cv2.imread(filename)

                if image is not None:
                    self.current_image = image
                    self.display_image(image)
                    self.add_log_message(f"成功导入图像: {filename}")

                    # 显示图像信息
                    height, width = image.shape[:2]
                    self.status_var.set(f"图像已导入 - 尺寸: {width}x{height}")

                    # 显示图像预览信息
                    self._show_image_info(filename, width, height)

                else:
                    messagebox.showerror("错误", "无法加载图像文件，请检查文件格式")

        except Exception as e:
            logger.error(f"导入图像失败: {e}")
            messagebox.showerror("错误", f"导入图像失败: {e}")

    def _show_image_info(self, filename: str, width: int, height: int):
        """显示图像信息对话框"""
        try:
            import os
            file_size = os.path.getsize(filename)
            file_size_mb = file_size / (1024 * 1024)

            info_text = f"""图像信息:
文件名: {os.path.basename(filename)}
路径: {filename}
尺寸: {width} x {height} 像素
文件大小: {file_size_mb:.2f} MB
格式: {os.path.splitext(filename)[1].upper()}

图像已成功加载，可以进行OCR识别和文本搜索。"""

            messagebox.showinfo("图像信息", info_text)

        except Exception as e:
            logger.warning(f"显示图像信息失败: {e}")

    def generate_code(self):
        """生成代码"""
        try:
            if not self.current_search_results:
                messagebox.showwarning("警告", "没有搜索结果可用于代码生成\n请先执行文本搜索")
                return

            # 创建代码生成对话框
            self._show_code_generation_dialog()

        except Exception as e:
            logger.error(f"代码生成失败: {e}")
            messagebox.showerror("错误", f"代码生成失败: {e}")

    def _show_code_generation_dialog(self):
        """显示代码生成对话框"""
        try:
            # 创建对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("代码生成选项")
            dialog.geometry("500x400")
            dialog.resizable(False, False)

            # 模态对话框
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
            y = (dialog.winfo_screenheight() // 2) - (400 // 2)
            dialog.geometry(f"500x400+{x}+{y}")

            # 创建界面
            main_frame = ttk.Frame(dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(main_frame, text="代码生成选项", font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 20))

            # 脚本名称
            name_frame = ttk.Frame(main_frame)
            name_frame.pack(fill=tk.X, pady=5)
            ttk.Label(name_frame, text="脚本名称:").pack(side=tk.LEFT)
            script_name_var = tk.StringVar(value="ocr_automation_script")
            ttk.Entry(name_frame, textvariable=script_name_var).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

            # 生成类型
            type_frame = ttk.LabelFrame(main_frame, text="生成类型")
            type_frame.pack(fill=tk.X, pady=10)

            generation_type = tk.StringVar(value="standalone")
            ttk.Radiobutton(type_frame, text="独立脚本 (单文件)",
                           variable=generation_type, value="standalone").pack(anchor=tk.W, padx=10, pady=5)
            ttk.Radiobutton(type_frame, text="模块化项目 (多文件结构)",
                           variable=generation_type, value="modular").pack(anchor=tk.W, padx=10, pady=5)

            # 选项
            options_frame = ttk.LabelFrame(main_frame, text="选项")
            options_frame.pack(fill=tk.X, pady=10)

            include_gui_var = tk.BooleanVar(value=False)
            ttk.Checkbutton(options_frame, text="包含GUI界面",
                           variable=include_gui_var).pack(anchor=tk.W, padx=10, pady=5)

            include_image_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(options_frame, text="包含当前图像路径",
                           variable=include_image_var).pack(anchor=tk.W, padx=10, pady=5)

            # 搜索配置预览
            preview_frame = ttk.LabelFrame(main_frame, text="搜索配置预览")
            preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)

            preview_text = tk.Text(preview_frame, height=8, wrap=tk.WORD)
            preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=preview_text.yview)
            preview_text.configure(yscrollcommand=preview_scrollbar.set)

            preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

            # 填充预览内容
            self._fill_preview_text(preview_text)

            # 按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=10)

            def on_generate():
                try:
                    # 收集搜索配置
                    search_configs = []
                    for result in self.current_search_results:
                        search_configs.append({
                            'search_term': result.search_term,
                            'mode': result.search_mode.value,
                            'similarity_threshold': 0.8
                        })

                    script_name = script_name_var.get().strip()
                    if not script_name:
                        script_name = "ocr_automation_script"

                    # 生成代码
                    if generation_type.get() == "standalone":
                        output_path = self.code_generator.generate_standalone_script(
                            search_configs=search_configs,
                            image_path="current_image.png" if include_image_var.get() else None,
                            script_name=script_name,
                            include_gui=include_gui_var.get()
                        )
                        success_msg = f"独立脚本已生成:\n{output_path}"
                    else:
                        output_path = self.code_generator.generate_modular_project(
                            search_configs=search_configs,
                            project_name=script_name
                        )
                        success_msg = f"模块化项目已生成:\n{output_path}"

                    # 如果包含图像，保存当前图像
                    if include_image_var.get() and self.current_image is not None:
                        import cv2
                        if generation_type.get() == "standalone":
                            image_path = os.path.join(os.path.dirname(output_path), "current_image.png")
                        else:
                            image_path = os.path.join(output_path, "current_image.png")
                        cv2.imwrite(image_path, self.current_image)

                    messagebox.showinfo("成功", success_msg)
                    dialog.destroy()

                    # 询问是否打开生成的文件夹
                    if messagebox.askyesno("打开文件夹", "是否打开生成的文件夹？"):
                        import subprocess
                        if generation_type.get() == "standalone":
                            folder_path = os.path.dirname(output_path)
                        else:
                            folder_path = output_path
                        subprocess.Popen(f'explorer "{folder_path}"')

                except Exception as e:
                    logger.error(f"代码生成失败: {e}")
                    messagebox.showerror("错误", f"代码生成失败: {e}")

            ttk.Button(button_frame, text="生成", command=on_generate).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            logger.error(f"显示代码生成对话框失败: {e}")

    def _fill_preview_text(self, text_widget):
        """填充预览文本"""
        try:
            text_widget.delete(1.0, tk.END)

            if not self.current_search_results:
                text_widget.insert(tk.END, "没有搜索结果可预览")
                return

            preview_content = "将生成以下搜索配置:\n\n"

            for i, result in enumerate(self.current_search_results, 1):
                preview_content += f"{i}. 搜索词: '{result.search_term}'\n"
                preview_content += f"   模式: {result.search_mode.value}\n"
                preview_content += f"   匹配文本: '{result.match_text}'\n"
                preview_content += f"   匹配分数: {result.match_score:.3f}\n"
                preview_content += f"   坐标: {result.ocr_result.center}\n\n"

            text_widget.insert(tk.END, preview_content)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            logger.warning(f"填充预览文本失败: {e}")

    def show_file_manager(self):
        """显示文件管理器"""
        try:
            file_manager_dialog = FileManagerDialog(self.root)
            file_manager_dialog.show()
        except Exception as e:
            logger.error(f"显示文件管理器失败: {e}")
            messagebox.showerror("错误", f"显示文件管理器失败: {e}")

    def _show_not_implemented(self, feature_name: str):
        """显示功能未实现提示"""
        messagebox.showinfo("提示", f"功能 '{feature_name}' 正在开发中，敬请期待！")

    def show_help(self):
        """显示帮助"""
        try:
            help_system.show_quick_start_guide(self.root)
        except Exception as e:
            logger.error(f"显示帮助失败: {e}")
            messagebox.showerror("错误", f"显示帮助失败: {e}")

    def show_about(self):
        """显示关于对话框"""
        about_text = """OCR屏幕识别与自动化工具 v2.0

基于PaddleOCR的智能屏幕文本识别和自动化操作工具

主要功能:
• 屏幕截图和图像导入
• 高精度OCR文本识别
• 智能文本搜索和匹配
• 自动化点击操作
• 代码生成和导出
• 文件管理和清理

开发团队: Augment Agent
更新时间: 2025-07-26
"""
        messagebox.showinfo("关于", about_text)

    def _on_image_click(self, x: int, y: int):
        """图像点击回调"""
        try:
            logger.info(f"图像点击: ({x}, {y})")
            # 可以在这里添加点击处理逻辑
        except Exception as e:
            logger.warning(f"处理图像点击失败: {e}")

    def _on_coordinate_change(self, x: int, y: int):
        """坐标变化回调"""
        try:
            # 更新状态栏或其他UI元素
            pass
        except Exception as e:
            logger.warning(f"处理坐标变化失败: {e}")

    def _display_image_fallback(self, image: np.ndarray):
        """图像显示的兼容性方法"""
        try:
            # 转换为PIL图像
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)

            pil_image = Image.fromarray(image_rgb)

            # 调整图像大小以适应显示
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                # 计算缩放比例
                scale_x = canvas_width / pil_image.width
                scale_y = canvas_height / pil_image.height
                scale = min(scale_x, scale_y, 1.0)  # 不放大

                if scale < 1.0:
                    new_width = int(pil_image.width * scale)
                    new_height = int(pil_image.height * scale)
                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 转换为Tkinter图像
            self.photo = ImageTk.PhotoImage(pil_image)

            # 清除画布并显示图像
            self.image_canvas.delete("all")
            self.image_canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)

            # 更新滚动区域
            self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))

        except Exception as e:
            logger.error(f"兼容性图像显示失败: {e}")

    def setup_shortcuts(self):
        """设置快捷键绑定"""
        try:
            # 图像获取快捷键
            self.root.bind('<Control-1>', lambda e: self.capture_fullscreen())
            self.root.bind('<Control-2>', lambda e: self.capture_region())
            self.root.bind('<Control-3>', lambda e: self.interactive_region_capture())
            self.root.bind('<Control-o>', lambda e: self.import_image())
            self.root.bind('<Control-O>', lambda e: self.import_image())

            # 识别分析快捷键
            self.root.bind('<Control-r>', lambda e: self.run_ocr())
            self.root.bind('<Control-R>', lambda e: self.run_ocr())
            self.root.bind('<Control-f>', lambda e: self.show_text_search())
            self.root.bind('<Control-F>', lambda e: self.show_text_search())
            self.root.bind('<Control-m>', lambda e: self.run_template_match())
            self.root.bind('<Control-M>', lambda e: self.run_template_match())

            # 自动化操作快捷键
            self.root.bind('<Control-t>', lambda e: self.test_click())
            self.root.bind('<Control-T>', lambda e: self.test_click())
            self.root.bind('<Control-b>', lambda e: self.batch_click_results())
            self.root.bind('<Control-B>', lambda e: self.batch_click_results())

            # 工具快捷键
            self.root.bind('<Control-g>', lambda e: self.generate_code())
            self.root.bind('<Control-G>', lambda e: self.generate_code())
            self.root.bind('<Control-e>', lambda e: self.show_file_manager())
            self.root.bind('<Control-E>', lambda e: self.show_file_manager())
            self.root.bind('<Control-comma>', lambda e: self.open_config_dialog())

            # 通用快捷键
            self.root.bind('<Control-s>', lambda e: self.save_screenshot())
            self.root.bind('<Control-S>', lambda e: self.save_screenshot())
            self.root.bind('<F1>', lambda e: self.show_help())
            self.root.bind('<Escape>', lambda e: self.root.focus_set())  # 重置焦点

            logger.info("快捷键绑定完成")
            self.add_log_message("快捷键已启用")

        except Exception as e:
            logger.error(f"设置快捷键失败: {e}")
            self.add_log_message(f"快捷键设置失败: {e}", "ERROR")

    def run(self):
        """运行主窗口"""
        try:
            self.add_log_message("应用程序启动")
            self.root.mainloop()
        except Exception as e:
            logger.error(f"运行主窗口失败: {e}")
            messagebox.showerror("错误", f"应用程序运行失败: {e}")


def main():
    """主函数"""
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        print(f"启动应用程序失败: {e}")


if __name__ == "__main__":
    main()
