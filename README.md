# 屏幕识别与自动化GUI应用程序

基于五层架构设计的完整屏幕识别与自动化解决方案，集成OCR文本识别、OpenCV模板匹配、PyAutoGUI自动化操作等核心功能。

## 功能特性

### 核心功能
- **屏幕截图**: 支持全屏和区域截图
- **OCR文本识别**: 基于PaddleOCR的高精度中英文识别
- **🆕 高级文本搜索**: 多种搜索模式（精确/模糊/正则/相似度匹配）
- **🆕 可视化标注**: 高亮显示搜索结果，多色标记不同目标
- **模板匹配**: OpenCV多尺度模板匹配
- **🆕 智能自动化**: 基于OCR结果的精确点击和批量操作
- **自动化操作**: 鼠标点击、键盘输入、拖拽等操作
- **实时日志**: 彩色编码的实时日志显示

### 技术架构
- **截图与预处理层**: 图像捕获、灰度化、二值化、形态学处理
- **文本识别层**: PaddleOCR集成，支持MKLDNN加速
- **图像匹配层**: OpenCV模板匹配，多尺度搜索
- **动作执行层**: PyAutoGUI操作封装，安全点击机制
- **控制与日志层**: 配置管理、日志记录、GUI界面

## 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8 或更高版本
- **内存**: 建议 4GB 以上
- **存储**: 至少 2GB 可用空间

## 快速开始

### 方法一：一键安装（推荐）

1. 下载项目到本地
2. 双击运行 `install.bat` （使用ANSI/GBK编码，避免乱码）
3. 等待安装完成（约5-10分钟，取决于网络速度）
4. 选择启动方式：
   - `start_app.bat` - 完整启动（推荐）
   - `quick_start.bat` - 快速启动
   - 手动运行：`venv\Scripts\python.exe main.py`

### 方法二：手动安装

```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 运行应用
python main.py
```

## 使用说明

### 基本操作

1. **截图功能**
   - 点击"全屏截图"捕获整个屏幕
   - 截图将显示在右侧图像区域

2. **OCR识别**
   - 先进行截图
   - 点击"OCR识别"进行文字识别
   - 调整置信度滑块设置识别阈值
   - 识别结果显示在下方结果列表

3. **模板匹配**
   - 先进行截图
   - 点击"模板匹配"选择模板图片
   - 匹配结果显示在结果列表

4. **自动点击**
   - 双击结果列表中的项目可自动点击对应位置
   - 或使用"点击测试"按钮测试点击功能

### 配置管理

- 点击"配置"按钮打开配置对话框
- 可调整OCR参数、匹配阈值、动作延时等
- 配置自动保存到 `config.yaml` 文件

### 🆕 文本搜索功能

#### 基本搜索
1. 先进行屏幕截图获取图像
2. 在"文本搜索"面板输入要查找的文本
3. 选择搜索模式：
   - **精确匹配**: 完全匹配指定文本
   - **包含匹配**: 文本包含指定关键词
   - **模糊匹配**: 允许拼写错误的容错匹配
   - **正则表达式**: 使用正则表达式进行复杂匹配
   - **相似度匹配**: 基于文本相似度的匹配
4. 点击"搜索"按钮执行搜索
5. 查看右侧图像中的高亮标注和结果列表

#### 高级功能
- **搜索历史**: 自动保存搜索词，可从下拉框快速选择
- **批量点击**: 对所有搜索结果执行自动点击操作
- **结果导出**: 将搜索结果导出为文本或CSV文件
- **可视化标注**: 不同搜索词使用不同颜色高亮显示

#### 使用技巧
- 调整相似度阈值来控制模糊匹配的严格程度
- 使用正则表达式搜索复杂模式，如 `用户\d+` 匹配"用户123"
- 批量操作前建议先确认搜索结果的准确性
- 可以导出搜索结果进行进一步分析

### 日志查看

- 左侧日志面板实时显示运行状态
- 不同级别的日志使用不同颜色显示
- 支持自动滚动和行数限制

## 项目结构

```
Automan/
├── core/                   # 核心功能模块
│   ├── screen.py          # 截图与预处理
│   ├── ocr.py             # 文本识别
│   ├── match.py           # 图像匹配
│   └── action.py          # 动作执行
├── utils/                  # 工具模块
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志模块
│   └── visualization.py   # 🆕 可视化标注工具
├── gui/                    # GUI界面
│   ├── main_window.py     # 主窗口
│   └── config_dialog.py   # 配置对话框
├── templates/              # 模板图片存储
├── logs/                   # 日志文件
├── temp/                   # 临时文件
├── docs/                   # 📚 文档目录
│   └── OCR_SEARCH_GUIDE.md # 🆕 OCR搜索功能详细指南
├── tests/                  # 测试文件
├── venv/                   # 虚拟环境
├── main.py                 # 主程序入口
├── config.yaml             # 配置文件
├── requirements.txt        # 依赖列表
├── install.bat             # 安装脚本
├── start_app.bat           # 启动脚本
├── quick_start.bat         # 快速启动
├── test_ocr_search.py      # 🆕 OCR搜索功能测试
└── README.md               # 说明文档
```

## 配置说明

主要配置项在 `config.yaml` 文件中：

```yaml
# OCR配置
ocr:
  confidence_threshold: 0.8  # 置信度阈值
  enable_mkldnn: true        # 启用MKLDNN加速

# 模板匹配配置
template_match:
  threshold: 0.85            # 匹配阈值
  enable_multi_scale: true   # 启用多尺度匹配

# 动作执行配置
action:
  click_delay: 0.1           # 点击延时
  fail_safe: true            # 启用安全模式
```

## 故障排除

### 依赖冲突问题
如果安装时遇到依赖冲突错误，请运行：
```bash
fix_dependencies.bat
```

详细的故障排除指南请参考：[TROUBLESHOOTING.md](TROUBLESHOOTING.md)

### 常见问题

1. **依赖冲突**
   - 运行 `fix_dependencies.bat` 修复
   - 或查看 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)

2. **启动失败**
   - 检查Python版本是否为3.8+
   - 确认已安装所有依赖包
   - 查看日志文件获取详细错误信息

3. **OCR识别不准确**
   - 调整置信度阈值
   - 确保图像清晰度足够
   - 检查PaddleOCR是否正确安装

4. **模板匹配失败**
   - 确认模板图片格式正确
   - 调整匹配阈值
   - 尝试启用多尺度匹配

5. **自动点击不工作**
   - 检查是否启用了安全模式
   - 确认坐标计算正确
   - 查看日志中的错误信息

### 日志文件

- 应用日志: `logs/automation.log`
- 问题记录: `☹问题→☺解决☛日志.md`
- 故障排除: `TROUBLESHOOTING.md`

## 🆕 新功能详细说明

### OCR文本搜索功能

本版本新增了强大的OCR文本搜索功能，包括：

#### 核心特性
- **多种搜索模式**: 支持精确、包含、模糊、正则表达式、相似度等5种匹配模式
- **批量搜索**: 可同时搜索多个关键词，使用不同颜色标记
- **智能过滤**: 基于置信度和匹配分数的结果过滤
- **搜索历史**: 自动保存搜索记录，支持快速重用

#### 可视化增强
- **高亮标注**: 在原图上直接标注搜索结果
- **多色标记**: 不同搜索词使用不同颜色区分
- **边界框显示**: 精确显示文本区域边界
- **统计信息**: 提供详细的搜索结果统计

#### 自动化集成
- **智能点击**: 基于OCR结果的精确坐标点击
- **批量操作**: 支持对所有搜索结果执行批量点击
- **动作队列**: 可编程的操作序列执行
- **安全检查**: 坐标验证和边界检查

#### 使用场景
- **UI自动化测试**: 通过文本内容定位和操作界面元素
- **数据提取**: 从截图中提取特定文本信息
- **重复操作**: 批量处理包含特定文本的界面
- **质量检查**: 验证界面文本的正确性

### 详细文档

- 📖 **完整使用指南**: `docs/OCR_SEARCH_GUIDE.md`
- 🧪 **功能测试**: 运行 `test_ocr_search.py` 查看所有功能演示
- 🔧 **API参考**: 查看各模块的详细API文档

## 开发说明

### 扩展功能

1. **添加新的识别算法**
   - 在 `core/` 目录下创建新模块
   - 实现统一的接口规范
   - 在主窗口中集成新功能

2. **自定义GUI组件**
   - 在 `gui/` 目录下添加新组件
   - 遵循现有的设计模式
   - 更新主窗口布局

3. **配置新参数**
   - 在 `config.yaml` 中添加配置项
   - 在 `utils/config.py` 中添加默认值
   - 在配置对话框中添加控制项

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 邮箱: [Contact Email]
- 文档: [Documentation Link]
