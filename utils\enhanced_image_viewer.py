"""
增强的图像预览组件
提供缩放、拖拽、标注切换等交互功能
"""

import tkinter as tk
from tkinter import ttk
import cv2
import numpy as np
from PIL import Image, ImageTk
from typing import Optional, Tuple, List, Callable
import math

from utils.logger import get_logger

logger = get_logger("enhanced_image_viewer")


class EnhancedImageViewer:
    """增强的图像预览器"""
    
    def __init__(self, parent: tk.Widget):
        """
        初始化图像预览器
        
        Args:
            parent: 父容器
        """
        self.parent = parent
        self.current_image = None
        self.display_image = None
        self.photo_image = None
        
        # 缩放和平移参数
        self.scale_factor = 1.0
        self.min_scale = 0.1
        self.max_scale = 10.0
        self.pan_x = 0
        self.pan_y = 0
        
        # 拖拽状态
        self.is_dragging = False
        self.drag_start_x = 0
        self.drag_start_y = 0
        
        # 显示模式
        self.fit_mode = "fit_window"  # fit_window, original_size, custom
        self.show_annotations = True
        self.annotations = []
        
        # 回调函数
        self.on_click_callback = None
        self.on_coordinate_change_callback = None
        
        self._create_widgets()
        self._bind_events()
        
        logger.info("增强图像预览器初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        self.toolbar = ttk.Frame(self.main_frame)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # 缩放控制
        ttk.Label(self.toolbar, text="缩放:").pack(side=tk.LEFT, padx=2)
        
        self.zoom_var = tk.StringVar(value="100%")
        self.zoom_combo = ttk.Combobox(
            self.toolbar, 
            textvariable=self.zoom_var,
            values=["25%", "50%", "75%", "100%", "125%", "150%", "200%", "300%", "500%"],
            width=8,
            state="readonly"
        )
        self.zoom_combo.pack(side=tk.LEFT, padx=2)
        self.zoom_combo.bind("<<ComboboxSelected>>", self._on_zoom_change)
        
        # 缩放按钮
        ttk.Button(self.toolbar, text="放大", command=self.zoom_in, width=6).pack(side=tk.LEFT, padx=1)
        ttk.Button(self.toolbar, text="缩小", command=self.zoom_out, width=6).pack(side=tk.LEFT, padx=1)
        ttk.Button(self.toolbar, text="适应", command=self.fit_to_window, width=6).pack(side=tk.LEFT, padx=1)
        ttk.Button(self.toolbar, text="原始", command=self.original_size, width=6).pack(side=tk.LEFT, padx=1)
        
        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 显示选项
        self.show_annotations_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            self.toolbar, 
            text="显示标注", 
            variable=self.show_annotations_var,
            command=self._toggle_annotations
        ).pack(side=tk.LEFT, padx=2)
        
        # 坐标显示
        self.coord_label = ttk.Label(self.toolbar, text="坐标: (0, 0)")
        self.coord_label.pack(side=tk.RIGHT, padx=5)
        
        # 图像信息
        self.info_label = ttk.Label(self.toolbar, text="无图像")
        self.info_label.pack(side=tk.RIGHT, padx=5)
        
        # 图像显示区域
        self.canvas_frame = ttk.Frame(self.main_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建画布和滚动条
        self.canvas = tk.Canvas(self.canvas_frame, bg="white", highlightthickness=0)
        
        self.h_scrollbar = ttk.Scrollbar(self.canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.v_scrollbar = ttk.Scrollbar(self.canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=self.h_scrollbar.set, yscrollcommand=self.v_scrollbar.set)
        
        # 布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        self.h_scrollbar.grid(row=1, column=0, sticky="ew")
        self.v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        self.canvas_frame.grid_rowconfigure(0, weight=1)
        self.canvas_frame.grid_columnconfigure(0, weight=1)
    
    def _bind_events(self):
        """绑定事件"""
        # 鼠标事件
        self.canvas.bind("<Button-1>", self._on_click)
        self.canvas.bind("<B1-Motion>", self._on_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_release)
        self.canvas.bind("<Motion>", self._on_motion)
        
        # 鼠标滚轮缩放
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind("<Button-4>", self._on_mousewheel)  # Linux
        self.canvas.bind("<Button-5>", self._on_mousewheel)  # Linux
        
        # 键盘事件
        self.canvas.bind("<Key>", self._on_key)
        self.canvas.focus_set()
    
    def load_image(self, image: np.ndarray):
        """
        加载图像
        
        Args:
            image: OpenCV格式图像
        """
        try:
            self.current_image = image.copy()
            self.annotations = []
            
            # 更新图像信息
            height, width = image.shape[:2]
            self.info_label.config(text=f"尺寸: {width}x{height}")
            
            # 重置视图
            self.scale_factor = 1.0
            self.pan_x = 0
            self.pan_y = 0
            
            # 适应窗口
            self.fit_to_window()
            
            logger.info(f"图像加载成功: {width}x{height}")
            
        except Exception as e:
            logger.error(f"加载图像失败: {e}")
    
    def add_annotations(self, annotations: List[dict]):
        """
        添加标注
        
        Args:
            annotations: 标注列表，每个标注包含坐标和样式信息
        """
        self.annotations = annotations
        self._update_display()
    
    def _update_display(self):
        """更新图像显示"""
        if self.current_image is None:
            return
        
        try:
            # 获取显示图像
            display_image = self.current_image.copy()
            
            # 添加标注
            if self.show_annotations and self.annotations:
                display_image = self._draw_annotations(display_image)
            
            # 缩放图像
            if self.scale_factor != 1.0:
                height, width = display_image.shape[:2]
                new_width = int(width * self.scale_factor)
                new_height = int(height * self.scale_factor)
                display_image = cv2.resize(display_image, (new_width, new_height), 
                                         interpolation=cv2.INTER_LINEAR if self.scale_factor > 1 else cv2.INTER_AREA)
            
            # 转换为PIL图像
            if len(display_image.shape) == 3:
                display_image = cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(display_image)
            
            # 转换为Tkinter图像
            self.photo_image = ImageTk.PhotoImage(pil_image)
            
            # 更新画布
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            
            # 更新滚动区域
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
            # 更新缩放显示
            self.zoom_var.set(f"{int(self.scale_factor * 100)}%")
            
        except Exception as e:
            logger.error(f"更新显示失败: {e}")
    
    def _draw_annotations(self, image: np.ndarray) -> np.ndarray:
        """
        在图像上绘制标注
        
        Args:
            image: 输入图像
            
        Returns:
            带标注的图像
        """
        annotated_image = image.copy()
        
        for annotation in self.annotations:
            try:
                ann_type = annotation.get('type', 'rectangle')
                color = annotation.get('color', (0, 255, 0))
                thickness = annotation.get('thickness', 2)
                
                if ann_type == 'rectangle':
                    x, y, w, h = annotation['bbox']
                    cv2.rectangle(annotated_image, (int(x), int(y)), 
                                (int(x + w), int(y + h)), color, thickness)
                
                elif ann_type == 'circle':
                    center = annotation['center']
                    radius = annotation['radius']
                    cv2.circle(annotated_image, (int(center[0]), int(center[1])), 
                             int(radius), color, thickness)
                
                elif ann_type == 'text':
                    text = annotation['text']
                    position = annotation['position']
                    font_scale = annotation.get('font_scale', 0.7)
                    cv2.putText(annotated_image, text, (int(position[0]), int(position[1])),
                              cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness)
                
            except Exception as e:
                logger.warning(f"绘制标注失败: {e}")
        
        return annotated_image
    
    def zoom_in(self):
        """放大"""
        new_scale = min(self.scale_factor * 1.25, self.max_scale)
        self._set_scale(new_scale)
    
    def zoom_out(self):
        """缩小"""
        new_scale = max(self.scale_factor / 1.25, self.min_scale)
        self._set_scale(new_scale)
    
    def fit_to_window(self):
        """适应窗口大小"""
        if self.current_image is None:
            return
        
        try:
            # 获取画布尺寸
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width <= 1 or canvas_height <= 1:
                self.parent.after(100, self.fit_to_window)
                return
            
            # 获取图像尺寸
            img_height, img_width = self.current_image.shape[:2]
            
            # 计算缩放比例
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
            
            self._set_scale(scale)
            self.fit_mode = "fit_window"
            
        except Exception as e:
            logger.error(f"适应窗口失败: {e}")
    
    def original_size(self):
        """原始尺寸"""
        self._set_scale(1.0)
        self.fit_mode = "original_size"
    
    def _set_scale(self, scale: float):
        """设置缩放比例"""
        self.scale_factor = max(self.min_scale, min(scale, self.max_scale))
        self._update_display()
    
    def _on_zoom_change(self, event):
        """缩放选择改变"""
        try:
            zoom_text = self.zoom_var.get()
            if zoom_text.endswith('%'):
                zoom_percent = int(zoom_text[:-1])
                scale = zoom_percent / 100.0
                self._set_scale(scale)
                self.fit_mode = "custom"
        except Exception as e:
            logger.warning(f"缩放改变失败: {e}")
    
    def _toggle_annotations(self):
        """切换标注显示"""
        self.show_annotations = self.show_annotations_var.get()
        self._update_display()
    
    def _on_click(self, event):
        """鼠标点击事件"""
        self.is_dragging = True
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        
        # 计算图像坐标
        if self.current_image is not None:
            img_x, img_y = self._canvas_to_image_coords(event.x, event.y)
            if self.on_click_callback:
                self.on_click_callback(img_x, img_y)
    
    def _on_drag(self, event):
        """鼠标拖拽事件"""
        if self.is_dragging:
            dx = event.x - self.drag_start_x
            dy = event.y - self.drag_start_y
            
            # 移动画布视图
            self.canvas.scan_dragto(event.x, event.y, gain=1)
            
            self.drag_start_x = event.x
            self.drag_start_y = event.y
    
    def _on_release(self, event):
        """鼠标释放事件"""
        self.is_dragging = False
    
    def _on_motion(self, event):
        """鼠标移动事件"""
        if self.current_image is not None:
            img_x, img_y = self._canvas_to_image_coords(event.x, event.y)
            self.coord_label.config(text=f"坐标: ({img_x}, {img_y})")
            
            if self.on_coordinate_change_callback:
                self.on_coordinate_change_callback(img_x, img_y)
    
    def _on_mousewheel(self, event):
        """鼠标滚轮事件"""
        if event.delta > 0 or event.num == 4:
            self.zoom_in()
        else:
            self.zoom_out()
    
    def _on_key(self, event):
        """键盘事件"""
        if event.keysym == "plus" or event.keysym == "equal":
            self.zoom_in()
        elif event.keysym == "minus":
            self.zoom_out()
        elif event.keysym == "0":
            self.original_size()
        elif event.keysym == "f":
            self.fit_to_window()
    
    def _canvas_to_image_coords(self, canvas_x: int, canvas_y: int) -> Tuple[int, int]:
        """
        将画布坐标转换为图像坐标
        
        Args:
            canvas_x: 画布X坐标
            canvas_y: 画布Y坐标
            
        Returns:
            图像坐标 (x, y)
        """
        if self.current_image is None:
            return 0, 0
        
        # 考虑滚动偏移
        canvas_x += self.canvas.canvasx(0)
        canvas_y += self.canvas.canvasy(0)
        
        # 转换为图像坐标
        img_x = int(canvas_x / self.scale_factor)
        img_y = int(canvas_y / self.scale_factor)
        
        # 限制在图像范围内
        height, width = self.current_image.shape[:2]
        img_x = max(0, min(img_x, width - 1))
        img_y = max(0, min(img_y, height - 1))
        
        return img_x, img_y
    
    def set_click_callback(self, callback: Callable[[int, int], None]):
        """设置点击回调函数"""
        self.on_click_callback = callback
    
    def set_coordinate_callback(self, callback: Callable[[int, int], None]):
        """设置坐标变化回调函数"""
        self.on_coordinate_change_callback = callback
