"""
统一文件管理系统
提供文件组织、命名、清理和浏览功能
"""

import os
import shutil
import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json

from utils.logger import get_logger

logger = get_logger("file_manager")


@dataclass
class FileInfo:
    """文件信息"""
    path: str
    name: str
    size: int
    created_time: datetime.datetime
    modified_time: datetime.datetime
    file_type: str
    category: str


class FileManager:
    """统一文件管理器"""
    
    def __init__(self, base_dir: str = "data"):
        """
        初始化文件管理器
        
        Args:
            base_dir: 基础数据目录
        """
        self.base_dir = Path(base_dir)
        self.categories = {
            'screenshots': {
                'fullscreen': '全屏截图',
                'region': '区域截图',
                'imported': '导入图像'
            },
            'templates': {
                'user': '用户模板',
                'system': '系统模板'
            },
            'results': {
                'ocr': 'OCR结果',
                'match': '匹配结果',
                'search': '搜索结果'
            },
            'exports': {
                'code': '生成代码',
                'reports': '分析报告',
                'data': '导出数据'
            }
        }
        
        self._init_directories()
        logger.info("文件管理器初始化完成")
    
    def _init_directories(self):
        """初始化目录结构"""
        try:
            # 创建基础目录
            self.base_dir.mkdir(exist_ok=True)
            
            # 创建分类目录
            for category, subcategories in self.categories.items():
                category_path = self.base_dir / category
                category_path.mkdir(exist_ok=True)
                
                for subcat in subcategories.keys():
                    subcat_path = category_path / subcat
                    subcat_path.mkdir(exist_ok=True)
            
            logger.info(f"目录结构初始化完成: {self.base_dir}")
            
        except Exception as e:
            logger.error(f"初始化目录结构失败: {e}")
            raise
    
    def generate_filename(self, category: str, subcategory: str, 
                         prefix: str = "", suffix: str = "", 
                         extension: str = "png") -> str:
        """
        生成标准化文件名
        
        Args:
            category: 文件分类
            subcategory: 子分类
            prefix: 文件名前缀
            suffix: 文件名后缀
            extension: 文件扩展名
            
        Returns:
            生成的文件路径
        """
        try:
            # 生成时间戳
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 构建文件名
            filename_parts = []
            if prefix:
                filename_parts.append(prefix)
            filename_parts.append(timestamp)
            if suffix:
                filename_parts.append(suffix)
            
            filename = "_".join(filename_parts) + f".{extension}"
            
            # 构建完整路径
            file_path = self.base_dir / category / subcategory / filename
            
            return str(file_path)
            
        except Exception as e:
            logger.error(f"生成文件名失败: {e}")
            return str(self.base_dir / "temp" / f"unnamed_{timestamp}.{extension}")
    
    def save_file(self, data, category: str, subcategory: str, 
                  filename: Optional[str] = None, **kwargs) -> str:
        """
        保存文件
        
        Args:
            data: 文件数据
            category: 文件分类
            subcategory: 子分类
            filename: 指定文件名（可选）
            **kwargs: 其他参数
            
        Returns:
            保存的文件路径
        """
        try:
            if filename is None:
                filename = self.generate_filename(category, subcategory, **kwargs)
            else:
                # 确保文件在正确的目录中
                file_path = self.base_dir / category / subcategory / filename
                filename = str(file_path)
            
            # 确保目录存在
            Path(filename).parent.mkdir(parents=True, exist_ok=True)
            
            # 根据数据类型保存文件
            if hasattr(data, 'save'):  # PIL Image
                data.save(filename)
            elif isinstance(data, str):  # 文本数据
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(data)
            elif isinstance(data, (dict, list)):  # JSON数据
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            else:  # 二进制数据
                with open(filename, 'wb') as f:
                    f.write(data)
            
            logger.info(f"文件保存成功: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise
    
    def get_files(self, category: Optional[str] = None, 
                  subcategory: Optional[str] = None,
                  file_type: Optional[str] = None) -> List[FileInfo]:
        """
        获取文件列表
        
        Args:
            category: 文件分类过滤
            subcategory: 子分类过滤
            file_type: 文件类型过滤
            
        Returns:
            文件信息列表
        """
        try:
            files = []
            
            # 确定搜索路径
            if category and subcategory:
                search_paths = [self.base_dir / category / subcategory]
            elif category:
                search_paths = [self.base_dir / category / subcat 
                              for subcat in self.categories.get(category, {}).keys()]
            else:
                search_paths = []
                for cat, subcats in self.categories.items():
                    for subcat in subcats.keys():
                        search_paths.append(self.base_dir / cat / subcat)
            
            # 遍历文件
            for search_path in search_paths:
                if search_path.exists():
                    for file_path in search_path.iterdir():
                        if file_path.is_file():
                            # 获取文件信息
                            stat = file_path.stat()
                            file_info = FileInfo(
                                path=str(file_path),
                                name=file_path.name,
                                size=stat.st_size,
                                created_time=datetime.datetime.fromtimestamp(stat.st_ctime),
                                modified_time=datetime.datetime.fromtimestamp(stat.st_mtime),
                                file_type=file_path.suffix.lower(),
                                category=f"{search_path.parent.name}/{search_path.name}"
                            )
                            
                            # 应用过滤器
                            if file_type and file_info.file_type != file_type:
                                continue
                            
                            files.append(file_info)
            
            # 按修改时间排序
            files.sort(key=lambda x: x.modified_time, reverse=True)
            
            return files
            
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []
    
    def delete_file(self, file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        try:
            Path(file_path).unlink()
            logger.info(f"文件删除成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False
    
    def move_file(self, src_path: str, dst_category: str, dst_subcategory: str) -> str:
        """
        移动文件到指定分类
        
        Args:
            src_path: 源文件路径
            dst_category: 目标分类
            dst_subcategory: 目标子分类
            
        Returns:
            新文件路径
        """
        try:
            src = Path(src_path)
            dst_dir = self.base_dir / dst_category / dst_subcategory
            dst_dir.mkdir(parents=True, exist_ok=True)
            
            dst_path = dst_dir / src.name
            shutil.move(str(src), str(dst_path))
            
            logger.info(f"文件移动成功: {src_path} -> {dst_path}")
            return str(dst_path)
            
        except Exception as e:
            logger.error(f"移动文件失败: {e}")
            raise
    
    def cleanup_old_files(self, days: int = 30, category: Optional[str] = None) -> int:
        """
        清理旧文件
        
        Args:
            days: 保留天数
            category: 指定分类（可选）
            
        Returns:
            清理的文件数量
        """
        try:
            cutoff_time = datetime.datetime.now() - datetime.timedelta(days=days)
            deleted_count = 0
            
            files = self.get_files(category=category)
            for file_info in files:
                if file_info.modified_time < cutoff_time:
                    if self.delete_file(file_info.path):
                        deleted_count += 1
            
            logger.info(f"清理完成，删除了{deleted_count}个文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理文件失败: {e}")
            return 0
    
    def get_storage_info(self) -> Dict[str, any]:
        """
        获取存储信息
        
        Returns:
            存储统计信息
        """
        try:
            total_size = 0
            file_count = 0
            category_stats = {}
            
            for category, subcategories in self.categories.items():
                category_size = 0
                category_count = 0
                
                for subcategory in subcategories.keys():
                    subcat_path = self.base_dir / category / subcategory
                    if subcat_path.exists():
                        for file_path in subcat_path.iterdir():
                            if file_path.is_file():
                                size = file_path.stat().st_size
                                total_size += size
                                category_size += size
                                file_count += 1
                                category_count += 1
                
                category_stats[category] = {
                    'size': category_size,
                    'count': category_count,
                    'size_mb': round(category_size / 1024 / 1024, 2)
                }
            
            return {
                'total_size': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2),
                'file_count': file_count,
                'categories': category_stats
            }
            
        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {}
    
    def export_file_list(self, output_path: str) -> bool:
        """
        导出文件列表
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            files = self.get_files()
            file_data = []
            
            for file_info in files:
                file_data.append({
                    'name': file_info.name,
                    'path': file_info.path,
                    'size': file_info.size,
                    'size_mb': round(file_info.size / 1024 / 1024, 2),
                    'created_time': file_info.created_time.isoformat(),
                    'modified_time': file_info.modified_time.isoformat(),
                    'file_type': file_info.file_type,
                    'category': file_info.category
                })
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(file_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"文件列表导出成功: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出文件列表失败: {e}")
            return False
