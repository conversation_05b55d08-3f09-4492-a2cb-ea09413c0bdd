"""
代码生成器
根据当前OCR搜索配置生成独立的Python脚本
"""

import os
import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from utils.logger import get_logger
from core.ocr import SearchMode, SearchResult

logger = get_logger("code_generator")


class CodeGenerator:
    """代码生成器类"""
    
    def __init__(self):
        """初始化代码生成器"""
        self.template_dir = Path("templates/code")
        self.output_dir = Path("generated_scripts")
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_standalone_script(self, 
                                 search_configs: List[Dict[str, Any]],
                                 image_path: Optional[str] = None,
                                 script_name: str = "auto_generated_script",
                                 include_gui: bool = False) -> str:
        """
        生成独立的Python脚本
        
        Args:
            search_configs: 搜索配置列表
            image_path: 图像文件路径
            script_name: 脚本名称
            include_gui: 是否包含GUI界面
            
        Returns:
            生成的脚本文件路径
        """
        try:
            # 生成脚本内容
            script_content = self._generate_script_content(
                search_configs, image_path, include_gui
            )
            
            # 保存脚本文件
            script_filename = f"{script_name}.py"
            script_path = self.output_dir / script_filename
            
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            logger.info(f"独立脚本已生成: {script_path}")
            return str(script_path)
            
        except Exception as e:
            logger.error(f"生成独立脚本失败: {e}")
            raise
    
    def generate_modular_project(self,
                               search_configs: List[Dict[str, Any]],
                               project_name: str = "ocr_automation_project") -> str:
        """
        生成模块化项目结构
        
        Args:
            search_configs: 搜索配置列表
            project_name: 项目名称
            
        Returns:
            项目目录路径
        """
        try:
            project_dir = self.output_dir / project_name
            project_dir.mkdir(exist_ok=True)
            
            # 创建项目结构
            self._create_project_structure(project_dir)
            
            # 生成主脚本
            main_script = self._generate_main_script(search_configs)
            with open(project_dir / "main.py", 'w', encoding='utf-8') as f:
                f.write(main_script)
            
            # 生成配置文件
            config_content = self._generate_config_file(search_configs)
            with open(project_dir / "config.yaml", 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            # 生成requirements.txt
            requirements = self._generate_requirements()
            with open(project_dir / "requirements.txt", 'w', encoding='utf-8') as f:
                f.write(requirements)
            
            # 生成README
            readme = self._generate_readme(project_name)
            with open(project_dir / "README.md", 'w', encoding='utf-8') as f:
                f.write(readme)
            
            logger.info(f"模块化项目已生成: {project_dir}")
            return str(project_dir)
            
        except Exception as e:
            logger.error(f"生成模块化项目失败: {e}")
            raise
    
    def _generate_script_content(self, 
                               search_configs: List[Dict[str, Any]],
                               image_path: Optional[str],
                               include_gui: bool) -> str:
        """生成脚本内容"""
        
        # 脚本头部
        header = f'''#!/usr/bin/env python3
"""
自动生成的OCR自动化脚本
生成时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

import cv2
import numpy as np
import time
import sys
import os
from typing import List, Dict, Any, Optional

# 添加项目路径到sys.path（如果需要）
# sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import paddleocr
    import pyautogui
    import difflib
    import re
except ImportError as e:
    print(f"缺少必要的依赖包: {{e}}")
    print("请运行: pip install paddleocr pyautogui")
    sys.exit(1)

'''

        # OCR引擎类
        ocr_class = '''
class SimpleOCREngine:
    """简化的OCR引擎"""
    
    def __init__(self):
        """初始化OCR引擎"""
        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')
        print("OCR引擎初始化完成")
    
    def recognize_text(self, image):
        """识别图像中的文本"""
        try:
            results = self.ocr.ocr(image, cls=True)
            
            ocr_results = []
            if results and results[0]:
                for line in results[0]:
                    bbox = line[0]
                    text = line[1][0]
                    confidence = line[1][1]
                    
                    ocr_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'center': self._get_center(bbox)
                    })
            
            return ocr_results
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return []
    
    def _get_center(self, bbox):
        """计算边界框中心点"""
        x_coords = [point[0] for point in bbox]
        y_coords = [point[1] for point in bbox]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        return (center_x, center_y)
    
    def search_text(self, image, search_term, mode='contains', similarity_threshold=0.8):
        """搜索文本"""
        ocr_results = self.recognize_text(image)
        matches = []
        
        for result in ocr_results:
            text = result['text']
            match_score = 0.0
            
            if mode == 'exact':
                if text == search_term:
                    match_score = 1.0
            elif mode == 'contains':
                if search_term.lower() in text.lower():
                    match_score = len(search_term) / len(text)
            elif mode == 'fuzzy':
                similarity = difflib.SequenceMatcher(None, text.lower(), search_term.lower()).ratio()
                if similarity >= similarity_threshold:
                    match_score = similarity
            elif mode == 'regex':
                try:
                    pattern = re.compile(search_term, re.IGNORECASE)
                    if pattern.search(text):
                        match_score = 0.8  # 正则匹配给固定分数
                except:
                    continue
            
            if match_score > 0:
                result['match_score'] = match_score
                result['search_term'] = search_term
                matches.append(result)
        
        # 按匹配分数排序
        matches.sort(key=lambda x: x['match_score'], reverse=True)
        return matches

'''

        # 自动化操作类
        automation_class = '''
class SimpleAutomation:
    """简化的自动化操作类"""
    
    def __init__(self):
        """初始化自动化操作"""
        pyautogui.FAILSAFE = True
        print("自动化操作初始化完成")
    
    def click_result(self, result, offset=(0, 0)):
        """点击搜索结果"""
        try:
            center_x, center_y = result['center']
            click_x = center_x + offset[0]
            click_y = center_y + offset[1]
            
            print(f"点击位置: ({click_x}, {click_y}) - 文本: '{result['text']}'")
            pyautogui.click(click_x, click_y)
            return True
            
        except Exception as e:
            print(f"点击失败: {e}")
            return False
    
    def batch_click(self, results, delay=1.0):
        """批量点击"""
        success_count = 0
        for i, result in enumerate(results):
            if self.click_result(result):
                success_count += 1
            
            if i < len(results) - 1:  # 最后一个不需要等待
                time.sleep(delay)
        
        print(f"批量点击完成: {success_count}/{len(results)}")
        return success_count

'''

        # 主函数
        main_function = f'''
def main():
    """主函数"""
    print("开始执行OCR自动化脚本...")
    
    # 初始化组件
    ocr_engine = SimpleOCREngine()
    automation = SimpleAutomation()
    
    # 搜索配置
    search_configs = {search_configs}
    
    # 加载图像
    image_path = "{image_path or 'screenshot.png'}"
    
    if not os.path.exists(image_path):
        print("图像文件不存在，尝试截图...")
        # 截取全屏
        screenshot = pyautogui.screenshot()
        screenshot.save(image_path)
        print(f"截图已保存: {{image_path}}")
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法加载图像: {{image_path}}")
        return
    
    print(f"图像加载成功: {{image.shape}}")
    
    # 执行搜索和操作
    all_results = []
    
    for config in search_configs:
        search_term = config.get('search_term', '')
        mode = config.get('mode', 'contains')
        threshold = config.get('similarity_threshold', 0.8)
        
        print(f"\\n搜索: '{{search_term}}' (模式: {{mode}})")
        
        results = ocr_engine.search_text(image, search_term, mode, threshold)
        print(f"找到 {{len(results)}} 个匹配结果")
        
        for result in results:
            print(f"  - '{{result['text']}}' (分数: {{result['match_score']:.3f}})")
        
        all_results.extend(results)
    
    # 执行自动化操作
    if all_results:
        print(f"\\n准备执行 {{len(all_results)}} 个自动化操作...")
        
        # 等待用户确认
        try:
            input("按回车键开始执行自动化操作，或Ctrl+C取消...")
            automation.batch_click(all_results, delay=1.0)
        except KeyboardInterrupt:
            print("\\n用户取消操作")
    else:
        print("\\n没有找到匹配结果，无法执行自动化操作")
    
    print("\\n脚本执行完成")


if __name__ == "__main__":
    main()
'''

        # 组合完整脚本
        full_script = header + ocr_class + automation_class + main_function
        
        return full_script
    
    def _create_project_structure(self, project_dir: Path):
        """创建项目目录结构"""
        dirs = ['src', 'config', 'logs', 'temp', 'tests']
        for dir_name in dirs:
            (project_dir / dir_name).mkdir(exist_ok=True)
    
    def _generate_main_script(self, search_configs: List[Dict[str, Any]]) -> str:
        """生成主脚本"""
        return '''#!/usr/bin/env python3
"""
OCR自动化项目主脚本
"""

import yaml
from src.ocr_engine import OCREngine
from src.automation import AutomationEngine

def main():
    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 初始化引擎
    ocr_engine = OCREngine(config['ocr'])
    automation = AutomationEngine(config['automation'])
    
    # 执行任务
    for task in config['tasks']:
        print(f"执行任务: {task['name']}")
        # 任务执行逻辑
        pass

if __name__ == "__main__":
    main()
'''
    
    def _generate_config_file(self, search_configs: List[Dict[str, Any]]) -> str:
        """生成配置文件"""
        config_content = f'''# OCR自动化配置文件
# 生成时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

ocr:
  lang: 'ch'
  confidence_threshold: 0.8
  enable_mkldnn: true

automation:
  click_delay: 0.1
  batch_delay: 1.0
  fail_safe: true

tasks:
'''
        
        for i, config in enumerate(search_configs):
            config_content += f'''  - name: "task_{i+1}"
    search_term: "{config.get('search_term', '')}"
    mode: "{config.get('mode', 'contains')}"
    similarity_threshold: {config.get('similarity_threshold', 0.8)}
    action: "click"
'''
        
        return config_content
    
    def _generate_requirements(self) -> str:
        """生成依赖文件"""
        return '''# OCR自动化项目依赖
paddleocr==2.7.3
opencv-python==********
pyautogui==0.9.54
numpy==1.24.3
pillow==10.0.1
pyyaml==6.0.1
'''
    
    def _generate_readme(self, project_name: str) -> str:
        """生成README文件"""
        return f'''# {project_name}

自动生成的OCR自动化项目

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行

```bash
python main.py
```

## 项目结构

- `src/` - 源代码目录
- `config/` - 配置文件目录
- `logs/` - 日志文件目录
- `temp/` - 临时文件目录
- `tests/` - 测试文件目录

## 配置

编辑 `config/config.yaml` 文件来修改搜索和自动化参数。

## 生成时间

{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
'''
