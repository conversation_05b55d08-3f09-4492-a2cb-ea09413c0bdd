"""
文件管理对话框
提供文件浏览、管理和清理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
from typing import List, Optional
from datetime import datetime

from utils.file_manager import FileManager, FileInfo
from utils.logger import get_logger

logger = get_logger("file_manager_dialog")


class FileManagerDialog:
    """文件管理对话框"""
    
    def __init__(self, parent: tk.Widget):
        """
        初始化文件管理对话框
        
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.file_manager = FileManager()
        self.dialog = None
        self.current_files = []
        self.selected_files = []
        
        self._create_dialog()
        logger.info("文件管理对话框初始化完成")
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("文件管理器")
        self.dialog.geometry("900x600")
        self.dialog.resizable(True, True)
        
        # 模态对话框
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self._center_dialog()
        
        # 创建界面
        self._create_widgets()
        self._load_files()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 工具栏
        self._create_toolbar(main_frame)
        
        # 文件列表
        self._create_file_list(main_frame)
        
        # 状态栏
        self._create_status_bar(main_frame)
        
        # 按钮栏
        self._create_button_bar(main_frame)
    
    def _create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 分类过滤
        ttk.Label(toolbar, text="分类:").pack(side=tk.LEFT, padx=2)
        
        self.category_var = tk.StringVar(value="全部")
        categories = ["全部"] + list(self.file_manager.categories.keys())
        self.category_combo = ttk.Combobox(
            toolbar, 
            textvariable=self.category_var,
            values=categories,
            width=12,
            state="readonly"
        )
        self.category_combo.pack(side=tk.LEFT, padx=5)
        self.category_combo.bind("<<ComboboxSelected>>", self._on_category_change)
        
        # 文件类型过滤
        ttk.Label(toolbar, text="类型:").pack(side=tk.LEFT, padx=(20, 2))
        
        self.filetype_var = tk.StringVar(value="全部")
        filetypes = ["全部", ".png", ".jpg", ".jpeg", ".bmp", ".json", ".txt", ".py"]
        self.filetype_combo = ttk.Combobox(
            toolbar,
            textvariable=self.filetype_var,
            values=filetypes,
            width=8,
            state="readonly"
        )
        self.filetype_combo.pack(side=tk.LEFT, padx=5)
        self.filetype_combo.bind("<<ComboboxSelected>>", self._on_filetype_change)
        
        # 刷新按钮
        ttk.Button(toolbar, text="刷新", command=self._load_files).pack(side=tk.LEFT, padx=10)
        
        # 存储信息
        self.storage_label = ttk.Label(toolbar, text="")
        self.storage_label.pack(side=tk.RIGHT, padx=5)
    
    def _create_file_list(self, parent):
        """创建文件列表"""
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview
        columns = ("name", "category", "size", "type", "modified")
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.file_tree.heading("name", text="文件名")
        self.file_tree.heading("category", text="分类")
        self.file_tree.heading("size", text="大小")
        self.file_tree.heading("type", text="类型")
        self.file_tree.heading("modified", text="修改时间")
        
        # 设置列宽
        self.file_tree.column("name", width=300)
        self.file_tree.column("category", width=120)
        self.file_tree.column("size", width=80)
        self.file_tree.column("type", width=60)
        self.file_tree.column("modified", width=150)
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        
        self.file_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.file_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.file_tree.bind("<<TreeviewSelect>>", self._on_file_select)
        self.file_tree.bind("<Double-1>", self._on_file_double_click)
        self.file_tree.bind("<Button-3>", self._on_right_click)
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        self.file_count_label = ttk.Label(status_frame, text="")
        self.file_count_label.pack(side=tk.RIGHT)
    
    def _create_button_bar(self, parent):
        """创建按钮栏"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X)
        
        # 左侧按钮
        ttk.Button(button_frame, text="打开文件", command=self._open_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="打开文件夹", command=self._open_folder).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="删除文件", command=self._delete_files).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(button_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=10, fill=tk.Y)
        
        # 清理按钮
        ttk.Button(button_frame, text="清理旧文件", command=self._cleanup_old_files).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="导出列表", command=self._export_file_list).pack(side=tk.LEFT, padx=2)
        
        # 右侧按钮
        ttk.Button(button_frame, text="关闭", command=self._close_dialog).pack(side=tk.RIGHT, padx=2)
    
    def _load_files(self):
        """加载文件列表"""
        try:
            # 获取过滤条件
            category = None if self.category_var.get() == "全部" else self.category_var.get()
            file_type = None if self.filetype_var.get() == "全部" else self.filetype_var.get()
            
            # 获取文件列表
            self.current_files = self.file_manager.get_files(category=category, file_type=file_type)
            
            # 清空现有列表
            for item in self.file_tree.get_children():
                self.file_tree.delete(item)
            
            # 添加文件到列表
            for file_info in self.current_files:
                size_str = self._format_file_size(file_info.size)
                modified_str = file_info.modified_time.strftime("%Y-%m-%d %H:%M")
                
                self.file_tree.insert("", tk.END, values=(
                    file_info.name,
                    file_info.category,
                    size_str,
                    file_info.file_type,
                    modified_str
                ))
            
            # 更新状态
            self._update_status()
            
        except Exception as e:
            logger.error(f"加载文件列表失败: {e}")
            messagebox.showerror("错误", f"加载文件列表失败: {e}")
    
    def _format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        else:
            return f"{size / 1024 / 1024:.1f} MB"
    
    def _update_status(self):
        """更新状态信息"""
        try:
            # 文件数量
            file_count = len(self.current_files)
            total_size = sum(f.size for f in self.current_files)
            size_str = self._format_file_size(total_size)
            
            self.file_count_label.config(text=f"文件数: {file_count}, 总大小: {size_str}")
            
            # 存储信息
            storage_info = self.file_manager.get_storage_info()
            total_mb = storage_info.get('total_size_mb', 0)
            total_count = storage_info.get('file_count', 0)
            
            self.storage_label.config(text=f"总存储: {total_mb:.1f} MB ({total_count} 文件)")
            
        except Exception as e:
            logger.warning(f"更新状态失败: {e}")
    
    def _on_category_change(self, event):
        """分类改变事件"""
        self._load_files()
    
    def _on_filetype_change(self, event):
        """文件类型改变事件"""
        self._load_files()
    
    def _on_file_select(self, event):
        """文件选择事件"""
        selection = self.file_tree.selection()
        self.selected_files = []
        
        for item in selection:
            index = self.file_tree.index(item)
            if 0 <= index < len(self.current_files):
                self.selected_files.append(self.current_files[index])
        
        # 更新状态
        if self.selected_files:
            count = len(self.selected_files)
            self.status_label.config(text=f"已选择 {count} 个文件")
        else:
            self.status_label.config(text="就绪")
    
    def _on_file_double_click(self, event):
        """文件双击事件"""
        self._open_file()
    
    def _on_right_click(self, event):
        """右键菜单事件"""
        # 创建右键菜单
        context_menu = tk.Menu(self.dialog, tearoff=0)
        context_menu.add_command(label="打开文件", command=self._open_file)
        context_menu.add_command(label="打开文件夹", command=self._open_folder)
        context_menu.add_separator()
        context_menu.add_command(label="删除文件", command=self._delete_files)
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def _open_file(self):
        """打开文件"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择文件")
            return
        
        try:
            for file_info in self.selected_files:
                if os.path.exists(file_info.path):
                    os.startfile(file_info.path)  # Windows
                    # subprocess.run(["xdg-open", file_info.path])  # Linux
                    # subprocess.run(["open", file_info.path])  # macOS
        except Exception as e:
            logger.error(f"打开文件失败: {e}")
            messagebox.showerror("错误", f"打开文件失败: {e}")
    
    def _open_folder(self):
        """打开文件夹"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择文件")
            return
        
        try:
            file_info = self.selected_files[0]
            folder_path = os.path.dirname(file_info.path)
            if os.path.exists(folder_path):
                subprocess.Popen(f'explorer "{folder_path}"')  # Windows
                # subprocess.run(["xdg-open", folder_path])  # Linux
                # subprocess.run(["open", folder_path])  # macOS
        except Exception as e:
            logger.error(f"打开文件夹失败: {e}")
            messagebox.showerror("错误", f"打开文件夹失败: {e}")
    
    def _delete_files(self):
        """删除文件"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择文件")
            return
        
        # 确认删除
        count = len(self.selected_files)
        if not messagebox.askyesno("确认删除", f"确定要删除选中的 {count} 个文件吗？\n此操作不可恢复！"):
            return
        
        try:
            deleted_count = 0
            for file_info in self.selected_files:
                if self.file_manager.delete_file(file_info.path):
                    deleted_count += 1
            
            messagebox.showinfo("删除完成", f"成功删除 {deleted_count} 个文件")
            self._load_files()
            
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            messagebox.showerror("错误", f"删除文件失败: {e}")
    
    def _cleanup_old_files(self):
        """清理旧文件"""
        # 获取清理天数
        days = tk.simpledialog.askinteger("清理旧文件", "请输入保留天数:", initialvalue=30, minvalue=1, maxvalue=365)
        if days is None:
            return
        
        # 确认清理
        if not messagebox.askyesno("确认清理", f"确定要删除 {days} 天前的文件吗？\n此操作不可恢复！"):
            return
        
        try:
            deleted_count = self.file_manager.cleanup_old_files(days)
            messagebox.showinfo("清理完成", f"成功清理 {deleted_count} 个旧文件")
            self._load_files()
            
        except Exception as e:
            logger.error(f"清理文件失败: {e}")
            messagebox.showerror("错误", f"清理文件失败: {e}")
    
    def _export_file_list(self):
        """导出文件列表"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出文件列表",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if filename:
                if self.file_manager.export_file_list(filename):
                    messagebox.showinfo("导出成功", f"文件列表已导出到:\n{filename}")
                else:
                    messagebox.showerror("导出失败", "导出文件列表失败")
                    
        except Exception as e:
            logger.error(f"导出文件列表失败: {e}")
            messagebox.showerror("错误", f"导出文件列表失败: {e}")
    
    def _close_dialog(self):
        """关闭对话框"""
        self.dialog.destroy()
    
    def show(self):
        """显示对话框"""
        self.dialog.wait_window()
